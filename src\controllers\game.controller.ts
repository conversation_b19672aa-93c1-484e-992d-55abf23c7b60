import { Request, Response } from 'express';
import { GameService } from '../services/game.service';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import User, { UserAttributes } from '../models/user.model';
import { JwtPayload } from '../utils/token.utils';

export class GameController {
  static async initGames(req: Request, res: Response): Promise<Response> {
    try {
      const success = await GameService.initGames();
      if (success) {
        return res.json({
          status: 'success',
          message: 'Games initialized successfully'
        });
      }
      return res.status(500).json({
        status: 'error',
        message: 'Failed to initialize games'
      });
    } catch (error) {
      console.error('Error initializing games:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }

  static async getGame(req: Request, res: Response): Promise<Response> {
    try {
      const { gameId } = req.params;
      const jwtUser = req.user as JwtPayload;
      
      // Fetch full user data
      const user = await User.findByPk(jwtUser.id);
      if (!user) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      const domain = req.headers.origin || '';
      const exitUrl = req.headers.referer || '';

      const gameUrl = await GameService.getGameUrl(user.id, gameId, domain, exitUrl);
      return res.json({
        status: 'success',
        url: gameUrl
      });
    } catch (error) {
      console.error('Error getting game:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to get game URL'
      });
    }
  }

  static async callback(req: Request, res: Response): Promise<Response> {
    try {
      // Log the callback data
      const logPath = join(__dirname, '../logs');
      await writeFile(
        join(logPath, 'game_callback.txt'),
        JSON.stringify({ body: req.body, timestamp: new Date() }, null, 2)
      );

      const response = await GameService.handleCallback(req.body);
      return res.json(response);
    } catch (error) {
      console.error('Callback error:', error);
      return res.status(500).json({
        status: 'error',
        message: error instanceof Error ? error.message : 'Internal server error'
      });
    }
  }
}
