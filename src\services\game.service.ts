import axios from 'axios';
import Game from '../models/game.model';
import GameTransaction from '../models/game-transaction.model';
import User, { UserRole } from '../models/user.model';
import { NotFoundError } from '../utils/errors';
import { createHash } from 'crypto';
import { Op, QueryTypes } from 'sequelize'; // Added QueryTypes
import sequelize from '../config/database'; // Added sequelize
import { gameCache, CACHE_KEYS } from '../utils/cache.utils';
import { RoleService } from './role.service';
import { UserService } from './user.service';
import { redisService } from './redis.service';

interface GameListResponse {
  status: string;
  content: {
    gameList: Array<{
      id: string;
      name: string;
      title: string;
      img: string;
      device: string;
      categories: string;
      flash: boolean;
    }>;
  };
}

interface GameUrlResponse {
  status: string;
  content: {
    game: {
      url: string;
    };
  };
}

interface GameHistoryParams {
  page?: number;
  limit?: number;
  startDate?: Date;
  endDate?: Date;
}

interface GameHistoryResult {
  history: GameTransaction[];
  total: number;
}

export class GameService {
  private static readonly endpoint =  process.env.GAME_PROVIDER_API_URL_tbs2api || 'endpoint';
  private static readonly hallID =  process.env.GAME_PROVIDER_HALL_ID_bs2api || 'hallid';
  private static readonly hallKey =  process.env.GAME_PROVIDER_HALL_KEY_bs2api || 'hallkey';

  private static sign(data: Record<string, any>): string {
    console.log('Original data:', data);

    // Remove sign from data if present
    const dataToSign = { ...data };
    delete dataToSign.signature;

    console.log('Data after removing signature:', dataToSign);

    // Sort the dictionary by key (like ksort in PHP)
    const sortedData = Object.keys(dataToSign)
      .sort()
      .reduce((obj: Record<string, any>, key: string) => {
        obj[key] = dataToSign[key];
        return obj;
      }, {});

    console.log('Sorted data:', sortedData);

    // Convert the dictionary to a list of values
    const values = Object.values(sortedData);
    console.log('Values before key:', values);

    // Append the key to the list
    values.push(this.hallKey);
    console.log('Values after adding key:', values);

    // Join the values with ':' as the separator
    const concatenatedData = values.map(v => String(v)).join(':');
    console.log('Concatenated data:', concatenatedData);

    // Compute the SHA-256 hash
    const hash = createHash('sha256')
      .update(concatenatedData)
      .digest('hex');

    console.log('Final hash:', hash);
    return hash;
  }

  static async initGames(): Promise<boolean> {
    try {
      const response = await axios.post<GameListResponse>(this.endpoint, {
        cmd: 'gamesList',
        hall: this.hallID,
        key: this.hallKey,
        cdnUrl: '',
      });

      if (response.data.status === 'success') {
        // Clear existing games
        await Game.destroy({ where: {} });

        // Create new games
        const games = await Promise.all(
          response.data.content.gameList.map((game) =>
            Game.create({
              id: game.id,
              name: game.name,
              title: game.title,
              img: game.img,
              device: game.device,
              categories: game.categories,
              flash: game.flash,
            })
          )
        );

        // Clear the existing cache after successful initialization
        gameCache.del(CACHE_KEYS.CATEGORIES);
        gameCache.del(CACHE_KEYS.TITLES);

        // Update Redis cache
        await redisService.invalidateGameCache();
        await redisService.setAllGames(games);

        // Extract and cache categories and titles
        const categories = await Game.getDistinctCategories();
        const titles = await Game.getDistinctTitles();
        await redisService.setCategories(categories);
        await redisService.setTitles(titles);

        console.log('Game cache updated after initialization');
        return true;
      }
      return false;
    } catch (error) {
      console.error('Error initializing games:', error);
      return false;
    }
  }

  static async getGameUrl(userId: number, gameId: string, domain: string, exitUrl: string): Promise<string> {
    try {
      // Verify user exists and is active
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('User not found');
      }
      if (!user.is_active || user.is_banned) {
        throw new Error('User account is not active');
      }

      // Check if game exists in Redis cache first
      const cachedGame = await redisService.getGame(gameId);
      if (!cachedGame) {
        // If not in cache, check database
        const game = await Game.findByPk(gameId);
        if (!game) {
          throw new Error('Game not found');
        }
        // Cache the game for future use
        await redisService.setGame(gameId, game);
      }

      const response = await axios.post<GameUrlResponse>(this.endpoint + 'openGame/', {
        cmd: 'openGame',
        hall: this.hallID,
        domain: domain,
        exitUrl: exitUrl,
        language: 'en',
        key: this.hallKey,
        login: userId,
        gameId: gameId,
        cdnUrl: '',
        demo: '0'
      });

      if (response.data.status === 'success') {
        return response.data.content.game.url;
      }
      throw new Error('Failed to get game URL');
    } catch (error) {
      console.error('Error getting game URL:', error);
      throw error;
    }
  }

  static async handleCallback(data: any): Promise<any> {
    // Log incoming data for debugging
    console.log('Callback data:', {
      receivedHall: data.hall,
      expectedHall: this.hallID,
      hasKey: !!data.key,
      hasSign: !!data.signature,
      fullData: data
    });

    // Convert hall to string for comparison since it might come as number
    const hallId = String(data.hall);

    // Verify hall ID
    if (hallId !== String(this.hallID)) {
      console.log('Hall ID mismatch:', { received: hallId, expected: this.hallID });
      throw new Error('Invalid Hall');
    }

    // For callbacks, we expect a sign parameter
    if (!data.signature) {
      console.log('Missing signature in callback');
      throw new Error('Invalid Sign');
    }

    // Validate the signature
    const calculatedSign = this.sign(data);
    if (data.signature !== calculatedSign) {
      console.log('Invalid signature:', {
        received: data.signature,
        calculated: calculatedSign,
        data: data
      });
      throw new Error('Invalid Sign');
    }

    switch (data.cmd) {
      case 'getBalance':
        return this.getBalance(data);
      case 'writeBet':
        return this.writeBet(data);
      default:
        throw new Error('Unknown command');
    }
  }

  private static async getBalance(data: any): Promise<any> {
    const user = await User.findByPk(data.login);
    if (!user) {
      return {
        status: 'fail',
        error: 'User not found'
      };
    }

    if (!user.is_active || user.is_banned) {
      return {
        status: 'fail',
        error: 'User account is not active'
      };
    }

    return {
      status: 'success',
      content: {
        balance: user.balance,
        currency: user.currency
      }
    };
  }

  private static async writeBet(data: any): Promise<any> {
    const user = await User.findByPk(data.login);
    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Get game details to fetch the name
    const game = await Game.findByPk(data.gameId);
    if (!game) {
      throw new NotFoundError('Game not found');
    }

    if (data.bet <= 0) {
      return {
        status: 'fail',
        error: 'fail_bet'
      };
    }

    const userBalance = parseFloat(user.balance.toString());
    const betAmount = parseFloat(data.bet.toString());
    const winAmount = parseFloat(data.win.toString());

    if (userBalance < betAmount) {
      return {
        status: 'fail',
        error: 'fail_balance'
      };
    }

    const newBalance = userBalance - betAmount + winAmount;
    const transaction = await GameTransaction.create({
      userId: data.login,
      bet: data.bet,
      win: data.win,
      tradeId: data.tradeId,
      betInfo: data.betInfo,
      gameId: data.gameId,
      name: game.name,
      matrix: data.matrix,
      date: data.date,
      winLines: data.WinLines,
      sessionId: data.sessionId,
      balance_before: user.balance,
      balance_after: newBalance,
      action: data.action
    });

    // Update user balance
    await user.update({
      balance: newBalance
    });

    return {
      status: 'success',
      error: '',
      login: user.id,
      balance: newBalance.toFixed(2),
      currency: user.currency || 'USD',
      operationId: Math.floor(Math.random() * 9899999) + 100000
    };
  }

  static async getGameHistory(
    viewerId: number,
    params: GameHistoryParams
  ): Promise<GameHistoryResult> {
    const viewer = await User.findByPk(viewerId);
    if (!viewer) {
      throw new NotFoundError('User not found');
    }

    const whereClause: any = {};

    // Add date filtering if provided
    if (params.startDate || params.endDate) {
      whereClause.createdAt = {};
      if (params.startDate) {
        whereClause.createdAt[Op.gte] = params.startDate;
      }
      if (params.endDate) {
        whereClause.createdAt[Op.lte] = params.endDate;
      }
    }

    // Role-based filtering
    switch (viewer.role) {
      case UserRole.SUPEROWNER:
        // Owner can see all transactions
        break;

        case UserRole.OWNER:
        case UserRole.SUPERADMIN:
        case UserRole.ADMIN:
        case UserRole.CASHIER:
        // Get all descendant IDs (hierarchy depends on role)
        const descendantIds = await UserService.getDescendantIds(viewerId);
        whereClause.userId = {
          [Op.in]: descendantIds
        };
        break;

      case UserRole.PLAYER:
        // Players can only see their own transactions
        whereClause.userId = viewerId;
        break;

      default:
        throw new Error('Invalid role');
    }

    // Get transactions with pagination
    const { count, rows } = await GameTransaction.findAndCountAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: params.limit || 20,
      offset: ((params.page || 1) - 1) * (params.limit || 20),
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username']
        }
      ]
    });

    return {
      history: rows,
      total: count
    };
  }

  static async getGameHistoryByUserId(
    viewerId: number,
    targetUserId: number,
    params: GameHistoryParams
  ): Promise<GameHistoryResult> {
    const viewer = await User.findByPk(viewerId);
    if (!viewer) {
      throw new NotFoundError('User not found');
    }

    // Check if viewer has appropriate role
    if (![UserRole.SUPEROWNER, UserRole.OWNER, UserRole.SUPERADMIN, UserRole.ADMIN, UserRole.CASHIER].includes(viewer.role)) {
      throw new Error('Unauthorized access');
    }

    const targetUser = await User.findByPk(targetUserId);
    if (!targetUser) {
      throw new NotFoundError('Target user not found');
    }

    // Verify access based on role hierarchy
    let hasAccess = false;
    switch (viewer.role) {
      case UserRole.SUPEROWNER:
        // Owner can see all users
        hasAccess = true;
        break;

      case UserRole.OWNER:
      case UserRole.SUPERADMIN:
      case UserRole.ADMIN:
      case UserRole.CASHIER:
        // Get all descendant IDs (hierarchy depends on role)
        const descendantIds = await UserService.getDescendantIds(viewerId);
        hasAccess = descendantIds.includes(targetUserId);
        break;
    }

    if (!hasAccess) {
      throw new Error('Unauthorized to view this user\'s game history');
    }

    const whereClause: any = {
      userId: targetUserId
    };

    // Add date filtering if provided
    if (params.startDate || params.endDate) {
      whereClause.createdAt = {};
      if (params.startDate) {
        whereClause.createdAt[Op.gte] = params.startDate;
      }
      if (params.endDate) {
        whereClause.createdAt[Op.lte] = params.endDate;
      }
    }

    // Get transactions with pagination
    const { count, rows } = await GameTransaction.findAndCountAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: params.limit || 20,
      offset: ((params.page || 1) - 1) * (params.limit || 20),
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'username']
        }
      ]
    });

    return {
      history: rows,
      total: count
    };
  }
}
