import { Request, Response } from 'express';
import { GameSearchService } from '../services/game-search.service';
import { handleError } from '../utils/errors';
import { AppError } from '../utils/errors';

export class GameSearchController {
  static async getProvCate(req: Request, res: Response): Promise<Response> {
    try {
      const { type } = req.query;
      
      if (!type || (type !== 'categories' && type !== 'titles')) {
        return res.status(400).json({
          status: 'error',
          message: 'Invalid type parameter. Must be "categories" or "titles"'
        });
      }

      const results = await GameSearchService.getProvCate(type as 'categories' | 'titles');
      
      return res.json(results);
    } catch (error) {
      console.error('Prov-cate error:', error);
      if (error instanceof Error) {
        return handleError(new AppError(error.message), res);
      }
      return handleError(new AppError('An unexpected error occurred'), res);
    }
  }
  static async searchGames(req: Request, res: Response): Promise<Response> {
    try {
      // Validate and sanitize search parameters
      const filters = GameSearchService.validateSearchParams(req.query);

      // Execute search
      const result = await GameSearchService.searchGames(filters);

      return res.json({
        status: 'success',
        ...result
      });
    } catch (error) {
      console.error('Game search error:', error);
      if (error instanceof Error) {
        return handleError(new AppError(error.message), res);
      }
      return handleError(new AppError('An unexpected error occurred'), res);
    }
  }
}
