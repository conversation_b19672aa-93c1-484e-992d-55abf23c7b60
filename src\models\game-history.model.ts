import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database';
import User from './user.model';
import Game from './game.model';

export class GameHistory extends Model {
  public id!: number;
  public userId!: number;
  public gameId!: string;
  public betAmount!: number;
  public winAmount!: number;
  public outcome!: 'win' | 'lose';
  public readonly createdAt!: Date;
  public tradeId!: string;
}

GameHistory.init(
  {
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      field: 'user_id',
      references: {
        model: User,
        key: 'id',
      },
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: false,
      field: 'game_id',
      references: {
        model: Game,
        key: 'id',
      },
    },
    betAmount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      field: 'bet_amount',
    },
    winAmount: {
      type: DataTypes.DECIMAL(15, 2),
      defaultValue: 0,
      field: 'win_amount',
    },
    outcome: {
      type: DataTypes.ENUM('win', 'lose'),
      allowNull: false,
    },
    tradeId: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      field: 'trade_id',
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      field: 'created_at',
    },
  },
  {
    sequelize,
    tableName: 'game_history',
    timestamps: false,
  }
);
