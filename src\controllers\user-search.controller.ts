import { Request, Response } from 'express';
import { UserService } from '../services/user.service';
import { UserRole } from '../models/user.model';
import { validationResult } from 'express-validator/src/validation-result';

interface SearchFilters {
  username?: string;
  role?: UserRole[];
  isBanned?: boolean;
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
  level?: number;
}

export class UserSearchController {
  static async searchUsers(req: Request, res: Response): Promise<void> {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          status: 'error',
          errors: errors.array()
        });
        return;
      }

      const searcherId = req.user!.id;
      const filters: SearchFilters = {};
      
      // Username filter - already sanitized by express-validator
      if (req.query.username) {
        filters.username = req.query.username as string;
      }

      // Boolean filters - converted to boolean by express-validator
      if (req.query.isBanned !== undefined) {
        filters.isBanned = req.query.isBanned === 'true';
      }
      if (req.query.isActive !== undefined) {
        filters.isActive = req.query.isActive === 'true';
      }
      if (req.query.roles) {
        const roles = (req.query.roles as string).split(',');
        filters.role = roles.filter(role => Object.values(UserRole).includes(role as UserRole)) as UserRole[];
      }

      // Date filters - already validated and converted by express-validator
      if (req.query.startDate) {
        const startDate = new Date(req.query.startDate as string);
        if (!isNaN(startDate.getTime())) {
          filters.startDate = startDate;
        }
      }
      if (req.query.endDate) {
        const endDate = new Date(req.query.endDate as string);
        if (!isNaN(endDate.getTime())) {
          filters.endDate = endDate;
        }
      }

      // Pagination - already validated and converted to numbers by express-validator
      if (req.query.page) {
        filters.page = Math.max(1, parseInt(req.query.page as string));
      }
      if (req.query.limit) {
        filters.limit = Math.min(100, Math.max(1, parseInt(req.query.limit as string)));
      }
      if (req.query.level) {
        const level = parseInt(req.query.level as string);
        filters.level = [1, 2].includes(level) ? level : 1;
      }

      const result = await UserService.searchUsers(searcherId, filters);
      
      res.status(200).json({
        status: 'success',
        data: result
      });
    } catch (error: any) {
      if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else {
        console.error('Search users error:', error);
        res.status(500).json({
          status: 'error',
          message: 'Internal server error'
        });
      }
    }
  }
}
