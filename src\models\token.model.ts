import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database';
import User from './user.model';

export interface TokenAttributes {
  id: string;
  userId: number;
  refreshToken: string;
  accessToken: string;
  deviceInfo: string;
  ipAddress: string;
  isValid: boolean;
  expiresAt: Date;
  lastUsed: Date;
  User?: User;
}

class Token extends Model<TokenAttributes> implements TokenAttributes {
  public id!: string;
  public userId!: number;
  public refreshToken!: string;
  public accessToken!: string;
  public deviceInfo!: string;
  public ipAddress!: string;
  public isValid!: boolean;
  public expiresAt!: Date;
  public lastUsed!: Date;
  public User?: User;

  // Timestamps
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

Token.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    refreshToken: {
      type: DataTypes.STRING(1000),
      allowNull: false,
    },
    accessToken: {
      type: DataTypes.STRING(1000),
      allowNull: false,
    },
    deviceInfo: {
      type: DataTypes.STRING(500),
      allowNull: false,
    },
    ipAddress: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    isValid: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    lastUsed: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    sequelize,
    modelName: 'Token',
    tableName: 'tokens'
    // Removing all indexes to avoid key length issues
  }
);

// Define association
Token.belongsTo(User, {
  foreignKey: 'userId',
  as: 'User',
});

export default Token;
