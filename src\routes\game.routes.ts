import { Router } from 'express';
import { GameController } from '../controllers/game.controller';
import { GameSearchController } from '../controllers/game-search.controller';
import { GameHistoryController } from '../controllers/game-history.controller';
import { authenticate } from '../middleware/auth.middleware';
import { csrfProtection } from '../middleware/csrf.middleware';
import { RoleMiddleware } from '../middleware/role.middleware';
import { UserRole } from '../models/user.model';
import { gameSearchLimiter } from '../middleware/rate-limit.middleware';
import { validate } from '../middleware/validation.middleware';
import { query } from 'express-validator/src/middlewares/validation-chain-builders';

const router = Router();


// Game provider callback (no authentication needed)
router.post('/callback', GameController.callback);

// Apply authentication to all following routes
router.use(authenticate);

// Apply CSRF protection to all routes that modify data
router.use(csrfProtection);

// Get distinct categories or titles
router.get(
  '/prov-cate',
  gameSearchLimiter,
  validate([
    query('type')
      .exists()
      .withMessage('Type parameter is required')
      .isIn(['categories', 'titles'])
      .withMessage('Type must be either "categories" or "titles"')
  ]),
  GameSearchController.getProvCate
);

// Public game search route with rate limiting and validation
router.get(
  '/search',
  gameSearchLimiter,
  validate([
    query('name').optional().isString().trim().escape(),
    query('title').optional().isString().trim().escape(),
    query('categories')
      .optional()
      .isString()
      .trim()
      .custom((value: string) => {
        if (!value) return true;
        const categories: string[] = value.split(',').map((cat: string): string => cat.trim());
        return categories.every((cat: string): boolean => cat.length > 0);
      })
      .withMessage('Categories must be comma-separated strings'),
    query('device').optional().isString().trim().escape(),
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('sortBy').optional().isIn(['name', 'title']),
    query('sortOrder').optional().isIn(['ASC', 'DESC'])
  ]),
  GameSearchController.searchGames
);


// Game history routes
router.get(
  '/history',
  validate([
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('startDate').optional().isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate()
  ]),
  GameHistoryController.getHistory
);

// Get game history by userId (restricted to owner, superadmin, cashier)
router.get(
  '/history/user/:userId',
  RoleMiddleware.hasRole([UserRole.SUPEROWNER,UserRole.OWNER, UserRole.SUPERADMIN,UserRole.ADMIN, UserRole.CASHIER]),
  validate([
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('startDate').optional().isISO8601().toDate(),
    query('endDate').optional().isISO8601().toDate()
  ]),
  GameHistoryController.getHistoryByUserId
);

// Initialize games (owner only)
router.post(
  '/init',
  RoleMiddleware.hasRole([UserRole.SUPEROWNER]),
  GameController.initGames
);

// Get game URL
router.get(
  '/:gameId',
  RoleMiddleware.hasRole([UserRole.OWNER, UserRole.SUPERADMIN, UserRole.CASHIER, UserRole.PLAYER]),
  GameController.getGame
);

export default router;
