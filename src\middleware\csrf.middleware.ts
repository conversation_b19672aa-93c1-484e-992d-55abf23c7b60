import { Request, Response, NextFunction } from 'express';
import { TokenUtils } from '../utils/token.utils';

/**
 * CSRF protection middleware
 * Verifies that the CSRF token in the request header matches the one in the cookie
 * This protects against Cross-Site Request Forgery attacks
 */
export const csrfProtection = (req: Request, res: Response, next: NextFunction): Response | void => {
  // Skip CSRF check for GET, HEAD, OPTIONS requests (they should be safe)
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  try {
    // Get CSRF token from cookie
    const cookieToken = req.cookies?.csrf_token;
    
    // Get CSRF token from header
    const headerToken = req.headers['x-csrf-token'] as string;

    // If either token is missing
    if (!cookieToken || !headerToken) {
      return res.status(403).json({
        status: 'error',
        message: 'CSRF token missing'
      });
    }

    // Compare tokens
    if (cookieToken !== headerToken) {
      return res.status(403).json({
        status: 'error',
        message: 'Invalid CSRF token'
      });
    }

    // If tokens match, proceed
    next();
  } catch (error) {
    console.error('CSRF validation error:', error);
    return res.status(403).json({
      status: 'error',
      message: 'CSRF validation failed'
    });
  }
};
