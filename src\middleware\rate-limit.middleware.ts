import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { JwtPayload } from '../utils/token.utils';

declare module 'express' {
  interface Request {
    user?: JwtPayload;
  }
}

// General API rate limiter
export const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // Limit each IP to 100 requests per windowMs
  message: { message: 'Too many requests from this IP, please try again later.' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Always return a string - use user ID if available, otherwise fallback to IP
    return (req.user?.id?.toString() ?? req.ip) as string;
  }
});

// More strict limiter for authentication endpoints
export const authLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // Limit each IP to 5 login attempts per hour
  message: { status: 'error', message: 'Too many login attempts, please try again later' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Always return a string - use user ID if available, otherwise fallback to IP
    return (req.user?.id?.toString() ?? req.ip) as string;
  }
});

// Game search rate limiter
export const gameSearchLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // Limit each IP to 30 requests per minute
  message: { status: 'error', message: 'Too many search requests, please try again later' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID as part of the rate limit key, fallback to IP
    return (req.user?.id?.toString() ?? req.ip) as string;
  }
});

// Limiter for sensitive operations (password changes, user creation)
export const sensitiveOpsLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 100, // Limit each IP to 10 requests per hour
  message: { status: 'error', message: 'Too many sensitive operations, please try again later' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return (req.user?.id?.toString() ?? req.ip) as string;
  }
});

// Game operations limiter
export const provCateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // Limit each IP to 60 requests per minute
  message: { 
    status: 'error', 
    message: 'Too many dropdown requests, please try again later' 
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Use user ID as part of the rate limit key, fallback to IP
    return (req.user?.id?.toString() ?? req.ip) as string;
  }
});

export const gameLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // Limit each IP to 60 requests per minute
  message: { status: 'error', message: 'Too many game operations, please try again later' },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    return (req.user?.id?.toString() ?? req.ip) as string;
  }
});
