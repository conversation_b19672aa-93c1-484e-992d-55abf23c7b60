{"name": "casino-backend", "version": "1.0.0", "description": "Cash-based casino management system", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "format": "prettier --write \"src/**/*.ts\"", "test": "jest"}, "dependencies": {"@types/axios": "^0.9.36", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/crypto-js": "^4.2.2", "@types/express-rate-limit": "^5.1.3", "@types/node-cache": "^4.1.3", "@types/uuid": "^10.0.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^6.15.0", "helmet": "^7.2.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.0", "node-cache": "^5.1.2", "sequelize": "^6.37.7", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.3", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/express-validator": "^2.20.33", "@types/ioredis": "^4.28.10", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.6.0", "@types/sequelize": "^4.28.20", "@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "eslint": "^8.49.0", "mysql2": "^3.14.0", "nodemon": "^3.1.9", "prettier": "^3.5.3", "sequelize-cli": "^6.6.2", "ts-node": "^10.9.1", "typescript": "^5.8.3"}}