import { Router } from 'express';
import { Cash<PERSON>lowController } from '../controllers/cash-flow.controller';
import { CashFlowHistoryController } from '../controllers/cash-flow-history.controller';
import { authenticate } from '../middleware/auth.middleware';
import { csrfProtection } from '../middleware/csrf.middleware';
import { validateRequest } from '../middleware/validation.middleware';
import { body, query, param } from 'express-validator/src/middlewares/validation-chain-builders';

const router = Router();

router.use(authenticate);

// Apply CSRF protection to all routes that modify data
router.use(csrfProtection);

// Cash operations routes
router.post(
  '/add',
  [
    body('toUserId').isInt().withMessage('Invalid user ID'),
    body('amount').isFloat({ min: 0.99 }).withMessage('Amount must be greater than 0.99'),
    validateRequest
  ],
  CashFlowController.addCash
);

router.post(
  '/deduct',
  [
    body('fromUserId').isInt().withMessage('Invalid user ID'),
    body('amount').isFloat({ min: 0.99 }).withMessage('Amount must be greater than 0.99'),
    validateRequest
  ],
  CashFlowController.deductCash
);

// History viewing routes
router.get('/history/all', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO date'),
  query('type').optional().isIn(['add', 'deduct']).withMessage('Type must be either "add" or "deduct"'),
  validateRequest
], CashFlowHistoryController.getAllHistory);

router.get('/history/:userId', [
  param('userId').isInt().withMessage('Invalid user ID'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('startDate').optional().isISO8601().withMessage('Start date must be a valid ISO date'),
  query('endDate').optional().isISO8601().withMessage('End date must be a valid ISO date'),
  query('type').optional().isIn(['add', 'deduct']).withMessage('Type must be either "add" or "deduct"'),
  validateRequest
], CashFlowHistoryController.getCashFlowHistory);

export default router;
