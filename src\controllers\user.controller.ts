import { Request, Response } from 'express';
import User, { UserRole, UserAttributes } from '../models/user.model';
import { RoleService } from '../services/role.service';
import { TokenUtils, JwtPayload } from '../utils/token.utils';
import { Op } from 'sequelize';
import { NotFoundError } from '../utils/errors';
import Game from '../models/game.model';
import CashFlow from '../models/cash-flow.model';
import Token from '../models/token.model'; // Added import
import { UserService } from '../services/user.service';
import sequelize from '../config/database'; // Added sequelize import
import { QueryTypes } from 'sequelize'; // Added QueryTypes import
import jwt from 'jsonwebtoken';

interface UserTree {
  id: number;
  username: string;
  role: UserRole;
  balance: number;
  currency: string;
  is_banned: boolean;
  created_at?: Date;
  updated_at?: Date;
  children: UserTree[];
}
export class UserController {
  // Login user
  static async login(req: Request, res: Response): Promise<Response> {
    try {
      const { username, password } = req.body;

      // Find user by username
      const user = await User.findOne({ where: { username } });
      if (!user) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials'
        });
      }

      // Verify password
      const isValidPassword = await user.verifyPassword(password);
      if (!isValidPassword) {
        return res.status(401).json({
          status: 'error',
          message: 'Invalid credentials'
        });
      }

      // Check if user is active and not banned
      if (!user.is_active || user.is_banned) {
        return res.status(403).json({
          status: 'error',
          message: user.is_banned ? 'Account is banned' : 'Account is inactive'
        });
      }



      // Invalidate all existing sessions for this user
      await TokenUtils.invalidateAllUserTokens(user.id);

      // Generate new token pair
      const tokens = await TokenUtils.generateTokenPair(user, 'web', '0.0.0.0');

      // Set HTTP-only cookies
      const accessTokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
      const refreshTokenExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

      res.cookie('accessToken', tokens.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        expires: accessTokenExpiry,
        path: '/'
      });

      res.cookie('refreshToken', tokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        expires: refreshTokenExpiry,
        path: '/'
      });

      // Generate CSRF token
      const csrfToken = TokenUtils.generateCsrfToken();

      // Set CSRF token in a non-HttpOnly cookie so frontend can access it
      res.cookie('csrf_token', csrfToken, {
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        expires: accessTokenExpiry,
        path: '/'
      });

      // Return user data but not the tokens
      return res.json({
        status: 'success',
        data: {
          user: {
            id: user.id,
            username: user.username,
            role: user.role,
            balance: user.balance,
            currency: user.currency,
          },
          csrfToken: csrfToken // Include CSRF token in response
        }
      });
    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to log in'
      });
    }
  }

  // Create based on role hierarchy
  static async createUser(req: Request, res: Response): Promise<Response> {
    try {
      const { username, password, role, currency } = req.body;
      const creatorRole = req.user?.role as UserRole;
      const creatorId = req.user?.id;

      if (!creatorId) {
        return res.status(401).json({
          status: 'error',
          message: 'Authentication required'
        });
      }

      // Validate username length
      if (username.length < 5 || username.length > 20) {
        return res.status(400).json({
          status: 'error',
          message: 'Username must be between 5 and 20 characters'
        });
      }

      // Strict role hierarchy enforcement
      if (role === UserRole.SUPERADMIN) {
        if (creatorRole !== UserRole.SUPEROWNER && creatorRole !== UserRole.OWNER) {
          return res.status(403).json({
            status: 'error',
            message: 'Only superowners can create superadmins'
          });
        }
      } else if (role === UserRole.OWNER) {
        if (creatorRole !== UserRole.SUPEROWNER) {
          return res.status(403).json({
            status: 'error',
            message: 'Only superowners can create owners'
          });
        }
      } else if (role === UserRole.ADMIN) {
        if (creatorRole !== UserRole.SUPERADMIN) {
          return res.status(403).json({
            status: 'error',
            message: 'Only superadmins can create admins'
          });
        }
      } else if (role === UserRole.CASHIER) {
        if (creatorRole !== UserRole.ADMIN) {
          return res.status(403).json({
            status: 'error',
            message: 'Only admins can create cashiers'
          });
        }
      } else if (role === UserRole.PLAYER) {
        if (creatorRole !== UserRole.CASHIER) {
          return res.status(403).json({
            status: 'error',
            message: 'Only cashiers can create players'
          });
        }
      } else {
        return res.status(400).json({
          status: 'error',
          message: 'Invalid role specified'
        });
      }

      const newUser = await User.create({
        username,
        password,
        role,
        currency: currency || 'TND',
        parent_id: creatorId,
        balance: 0,
        is_active: true,
        is_banned: false,
      });

      return res.status(201).json({
        status: 'success',
        message: `${role} created successfully`,
        user: {
          id: newUser.id,
          username: newUser.username,
          role: newUser.role,
          currency: newUser.currency,
        },
      });
    } catch (error) {
      console.error('Creation error:', error);

      // Handle known error types
      if (error instanceof Error) {
        // Handle Sequelize unique constraint error
        if ((error as any).name === 'SequelizeUniqueConstraintError') {
          return res.status(400).json({
            status: 'error',
            message: 'Username already exists'
          });
        }

        // Handle username validation error
        if (error.message.includes('Username must be between')) {
          return res.status(400).json({
            status: 'error',
            message: error.message
          });
        }
      }

      // Handle unknown errors
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }

  /**
   * Change user password with role-based access control
   */
  static async changePassword(req: Request, res: Response): Promise<Response> {
    try {
      const { user_id, old_password, new_password } = req.body;
      const adminUser = req.user as JwtPayload;

      // Get the target user
      const targetUser = await User.findByPk(Number(user_id));
      if (!targetUser) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      const targetUserAttrs = targetUser.get();

      // Check if changing own password or has permission to change others
      const isOwnPassword = adminUser.id === targetUser.id;
      const canChangeOthers = RoleService.canManageRole(adminUser.role, targetUserAttrs.role);

      if (!isOwnPassword && !canChangeOthers) {
        return res.status(403).json({
          status: 'error',
          message: 'You do not have permission to change this user\'s password'
        });
      }

      // If changing own password, verify old password
      if (isOwnPassword) {
        if (!old_password) {
          return res.status(400).json({
            status: 'error',
            message: 'Current password is required when changing your own password'
          });
        }

        const isValidPassword = await targetUser.verifyPassword(old_password);
        if (!isValidPassword) {
          return res.status(401).json({
            status: 'error',
            message: 'Current password is incorrect'
          });
        }
      }

      // Update password
      await targetUser.update({ password: new_password });

      // Invalidate all tokens (treated as security-sensitive change)
      await TokenUtils.invalidateAllUserTokens(targetUser.id);
      console.log(`Invalidated tokens after password change for user ${targetUser.id}`);

      return res.json({
        status: 'success',
        message: 'Password changed successfully'
      });
    } catch (error) {
      console.error('Change password error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to change password'
      });
    }
  }

  static async getProfile(req: Request, res: Response): Promise<Response> {
    try {
      const adminUser = req.user as JwtPayload;
      if (!adminUser?.id) {
        return res.status(401).json({ status: 'error', message: 'Authentication required' });
      }

      const isViewingOthers = req.method === 'POST';
      const targetUserId = isViewingOthers ? Number(req.body.user_id) : adminUser.id;

      if (isNaN(targetUserId)) {
        return res.status(400).json({ status: 'error', message: 'Invalid user ID' });
      }

      // Permission checks for viewing others
      if (isViewingOthers) {
        if (![UserRole.SUPEROWNER, UserRole.OWNER, UserRole.SUPERADMIN, UserRole.ADMIN, UserRole.CASHIER].includes(adminUser.role)) {
          return res.status(403).json({ status: 'error', message: 'Insufficient permissions' });
        }

        const canView = await RoleService.canViewProfile(adminUser.id, adminUser.role, targetUserId);
        if (!canView) {
          return res.status(403).json({ status: 'error', message: 'Access denied' });
        }
      }

      // Single optimized query for user data
      const targetUser = await User.findByPk(targetUserId, {
        attributes: ['id', 'username', 'role', 'balance', 'currency', 'is_active', 'is_banned', 'last_login', 'parent_id', 'createdAt'],
        raw: true
      });

      if (!targetUser) {
        return res.status(404).json({ status: 'error', message: 'User not found' });
      }

      // Get all descendants count if viewing others
      let childCount = 0;
      let parent_username = null;
      if (isViewingOthers) {
        const descendants = await UserService.getDescendantIds(targetUserId);
        childCount = descendants.length;

      const parent = await User.findByPk(targetUser.parent_id, {
        attributes: [ 'username'],
        raw: true
      });
      if (parent) {
        parent_username = parent.username;
      }
    }

      const response = {
        status: 'success',
        data: isViewingOthers ? { ...targetUser, childCount, parent_username } : targetUser
      };

      return res.json(response);

    } catch (error) {
      console.error('Get profile error:', error);
      return res.status(500).json({ status: 'error', message: 'Failed to retrieve profile' });
    }
  }

  // Ban a user and all their descendants
  static async banUser(req: Request, res: Response): Promise<Response> {
    try {
      const { user_id } = req.body;
      const adminUser = req.user as JwtPayload;

      // Get the target user
      const targetUser = await User.findByPk(Number(user_id));
      if (!targetUser) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      const targetUserAttrs = targetUser.get();

      // Only SUPEROWNER can ban owner
      if (targetUserAttrs.role === UserRole.OWNER && adminUser.role !== UserRole.SUPEROWNER) {
        return res.status(403).json({
          status: 'error',
          message: 'Only superowner can ban owner'
        });
      }

      // Check role hierarchy
      if (!RoleService.canManageRole(adminUser.role, targetUserAttrs.role)) {
        return res.status(403).json({
          status: 'error',
          message: 'Cannot ban user with equal or higher role'
        });
      }

      // Get all descendants of the target user
      const descendants = await UserService.getDescendantIds(targetUser.id);

      // Ban the target user and all descendants
      await User.update(
        { is_banned: true },
        {
          where: {
            id: {
              [Op.in]: [targetUser.id, ...descendants]
            }
          }
        }
      );

      // Invalidate and delete all sessions and tokens for banned users in one call
      await TokenUtils.invalidateAllUserTokens(targetUser.id); // Invalidate target
      if (descendants.length > 0) {
          await Token.destroy({ where: { userId: { [Op.in]: descendants } } }); // Invalidate descendants
      }


      return res.json({
        status: 'success',
        message: 'User and descendants banned successfully',
        data: {
          bannedUsers: [targetUser.id, ...descendants]
        }
      });
    } catch (error) {
      console.error('Error banning user:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to ban user'
      });
    }
  }

  /**
   * Unban a user and all their descendants
   */
  static async unbanUser(req: Request, res: Response): Promise<Response> {
    try {
      const { user_id } = req.body;
      const adminUser = req.user as JwtPayload;

      // Get the target user
      const targetUser = await User.findByPk(Number(user_id));
      if (!targetUser) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      const targetUserAttrs = targetUser.get();

      // Check if user is not banned
      if (!targetUserAttrs.is_banned) {
        return res.status(400).json({
          status: 'error',
          message: 'User is not banned'
        });
      }

      // Check role hierarchy (allow SUPEROWNER to unban anyone)
      if (adminUser.role !== UserRole.SUPEROWNER && !RoleService.canManageRole(adminUser.role, targetUserAttrs.role)) {
        return res.status(403).json({
          status: 'error',
          message: 'Cannot unban user with equal or higher role'
        });
      }

      // Get all descendants of the target user
      const descendants = await UserService.getDescendantIds(targetUser.id);

      // Unban the target user and all their descendants
      await User.update(
        { is_banned: false },
        {
          where: {
            id: {
              [Op.in]: [targetUser.id, ...descendants]
            }
          }
        }
      );

      return res.json({
        status: 'success',
        message: 'User and descendants unbanned successfully',
        data: {
          unbannedUsers: [targetUser.id, ...descendants]
        }
      });
    } catch (error) {
      console.error('Error unbanning user:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to unban user'
      });
    }
  }

  // Refresh token
  static async refreshToken(req: Request, res: Response): Promise<Response> {
    try {
      // Get refresh token from cookie only
      const refreshToken = req.cookies.refreshToken;
      const accessToken = req.cookies.accessToken;

      if (!refreshToken) {
        return res.status(400).json({
          status: 'error',
          message: 'Something went wrong!'
        });
      }

      // Check if access token is expired
      if (accessToken) {
        try {
          // Decode the token to get expiration (without verifying signature)
          const decoded = jwt.decode(accessToken) as { exp?: number, id?: number };
          
          if (decoded && decoded.exp) {
            const expiryTime = new Date(decoded.exp * 1000);
            const now = new Date();
            
            // If token is expired, invalidate all user tokens
            if (expiryTime < now && decoded.id) {
              await TokenUtils.invalidateAllUserTokens(decoded.id);
              
              // Clear cookies
              res.clearCookie('accessToken', {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
                path: '/'
              });
              res.clearCookie('refreshToken', {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
                path: '/'
              });
              res.clearCookie('csrf_token', {
                httpOnly: false,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
                path: '/'
              });
              
              return res.status(401).json({
                status: 'error',
                message: 'Session expired',
                code: 'SESSION_EXPIRED'
              });
            }
          }
        } catch (decodeError) {
          console.error('Failed to decode token:', decodeError);
        }
      }

      try {
        // Verify and refresh tokens
        const tokens = await TokenUtils.refreshTokenPair(refreshToken);

        // Set HTTP-only cookies
        const accessTokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
        const refreshTokenExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days

        res.cookie('accessToken', tokens.accessToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
          expires: accessTokenExpiry,
          path: '/'
        });

        res.cookie('refreshToken', tokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
          expires: refreshTokenExpiry,
          path: '/'
        });

        // Generate new CSRF token
        const csrfToken = TokenUtils.generateCsrfToken();

        // Set CSRF token in a non-HttpOnly cookie
        res.cookie('csrf_token', csrfToken, {
          httpOnly: false,
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
          expires: accessTokenExpiry,
          path: '/'
        });

        // Return success with CSRF token but not the auth tokens
        return res.json({
          status: 'success',
          data: {
            csrfToken: csrfToken
          }
        });
      } catch (tokenError) {
        if (tokenError instanceof Error) {
          return res.status(401).json({
            status: 'error',
            message: tokenError.message
          });
        }
        throw tokenError;
      }
    } catch (error) {
      console.error('Refresh token error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }

  // Logout
  static async logout(req: Request, res: Response): Promise<Response> {
    try {
        // Get token from cookie only
        const accessToken = req.cookies.accessToken;

        // Try to invalidate token if available
        if (accessToken) {
            try {
                const decoded = await TokenUtils.verifyAccessToken(accessToken);
                await TokenUtils.invalidateToken(decoded.tokenId);
            } catch (tokenError) {
                // Continue with logout even if token verification fails
                console.log('Token verification failed during logout, continuing anyway');
            }
        }

        // Always clear cookies
        res.clearCookie('accessToken', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            path: '/'
        });

        res.clearCookie('refreshToken', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            path: '/'
        });

        // Clear CSRF token cookie
        res.clearCookie('csrf_token', {
            httpOnly: false,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            path: '/'
        });

        // Only set Clear-Site-Data for browser requests (not API calls)
        const isApiRequest = req.get('Accept')?.includes('application/json') ||
                           req.get('Content-Type')?.includes('application/json');

        if (!isApiRequest) {
            res.setHeader('Clear-Site-Data', '"cookies", "storage", "cache"');
        }

        return res.json({
            status: 'success',
            message: 'Successfully logged out'
        });
    } catch (error) {
        console.error('Logout error:', error);

        // Still try to clear cookies even if there's an error
        try {
            res.clearCookie('accessToken', {
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
            });
            res.clearCookie('refreshToken', {
                path: '/',
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
            });
            res.clearCookie('csrf_token', {
                path: '/',
                httpOnly: false,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax'
            });
        } catch (cookieError) {
            console.error('Error clearing cookies:', cookieError);
        }

        return res.status(500).json({
            status: 'error',
            message: 'Internal server error'
        });
    }
}


  /**
   * Get all descendants recursively with proper parent-child relationship check
   */
  public static async getStats(userId: number): Promise<any> {
    // Get all descendant IDs in the hierarchy
    const descendantIds = await UserService.getDescendantIds(userId);

    // Get the user's balance
    const user = await User.findByPk(userId, {
      attributes: ['balance']
    });

    // Get today's transactions
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const todayTransactions = await CashFlow.findAll({
      where: {
        [Op.or]: [
          { fromUserId: userId },
          { toUserId: userId }
        ],
        createdAt: {
          [Op.between]: [todayStart, todayEnd]
        }
      }
    });

    // Calculate totals
    let totalSold = 0;  // User sells to descendants
    let totalBought = 0;  // User buys from parent
    let totalTook = 0;  // User takes from descendants

    todayTransactions.forEach(transaction => {
      const amount = Number(transaction.amount);

      // User is fromUserId (initiates transaction)
      if (transaction.fromUserId === userId) {
        if (transaction.type === 'add') {
          totalSold += amount;  // Sell to descendants
        } else if (transaction.type === 'deduct') {
          totalTook += amount;  // Take from descendants
        }
      }
      // User is toUserId (parent initiates transaction)
      else if (transaction.toUserId === userId) {
        if (transaction.type === 'add') {
          totalBought += amount;  // Buy from parent
        }
      }
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Query the database for role, balance, username, and ban status of descendants
    const users = await User.findAll({
      where: {
        id: {
          [Op.in]: descendantIds
        }
      },
      attributes: ['id', 'username', 'role', 'balance', 'is_banned']
    });

    // Get total number of games
    const totalGames = await Game.count();

    // Get last 5 transactions in the hierarchy
    const lastTransactions = await CashFlow.findAll({
      where: {
        [Op.or]: [
          { fromUserId: { [Op.in]: descendantIds } },
          { toUserId: { [Op.in]: descendantIds } }
        ]
      },
      order: [['createdAt', 'DESC']],
      limit: 5,
      include: [
        {
          model: User,
          as: 'fromUser',
          attributes: ['id', 'username']
        },
        {
          model: User,
          as: 'toUser',
          attributes: ['id', 'username']
        }
      ]
    });

    // Get last 5 banned players
    const lastBannedPlayers = await User.findAll({
      where: {
        id: { [Op.in]: descendantIds },
        is_banned: true
      },
      order: [['updatedAt', 'DESC']],
      limit: 5,
      attributes: ['id', 'username']
    });

    // Calculate statistics
    const stats = {
      totalUsers: users.length,
      owner: users.filter(u => u.role === UserRole.OWNER).length,
      superadmin: users.filter(u => u.role === UserRole.SUPERADMIN).length,
      admin: users.filter(u => u.role === UserRole.ADMIN).length,
      cashier: users.filter(u => u.role === UserRole.CASHIER).length,
      player: users.filter(u => u.role === UserRole.PLAYER).length,
      banned: users.filter(u => u.is_banned).length,
      active: users.filter(u => !u.is_banned).length,
      userBalance: user.balance,
      totalGames,
      todayTransactions: {
        sold: totalSold,
        bought: totalBought,
        took: totalTook
      },
      lastTransactions: lastTransactions.map(t => ({
        id: t.id,
        amount: t.amount,
        type: t.type,
        fromUser: t.fromUser ? { id: t.fromUser.id, username: t.fromUser.username } : null,
        toUser: t.toUser ? { id: t.toUser.id, username: t.toUser.username } : null,
        createdAt: t.createdAt
      })),
      lastBannedPlayers: lastBannedPlayers.map(p => ({
        id: p.id,
        username: p.username
      })),
      topOwners: users
        .filter(u => u.role === UserRole.OWNER)
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 5)
        .map(u => ({ id: u.id, username: u.username, balance: u.balance })),
      topSuperadmins: users
        .filter(u => u.role === UserRole.SUPERADMIN)
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 5)
        .map(u => ({ id: u.id, username: u.username, balance: u.balance })),
      topAdmins: users
        .filter(u => u.role === UserRole.ADMIN)
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 5)
        .map(u => ({ id: u.id, username: u.username, balance: u.balance })),
      topCashiers: users
        .filter(u => u.role === UserRole.CASHIER)
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 5)
        .map(u => ({ id: u.id, username: u.username, balance: u.balance })),
      topPlayers: users
        .filter(u => u.role === UserRole.PLAYER)
        .sort((a, b) => b.balance - a.balance)
        .slice(0, 5)
        .map(u => ({ id: u.id, username: u.username, balance: u.balance }))
    };

    return stats;
  }

  static async getUserTree(req: Request, res: Response): Promise<Response> {
    try {
      const targetUserId = req.body.user_id;

      const treeQuery = `
        WITH RECURSIVE UserTreeData AS (
          -- Anchor member: Select the root user
          SELECT
            id, username, role, balance, currency, is_banned, created_at, updated_at, parent_id,
            CAST(id AS JSON) AS path, -- Use JSON array for path tracking
            1 as level
          FROM users
          WHERE id = :targetUserId

          UNION ALL

          -- Recursive member: Select children
          SELECT
            u.id, u.username, u.role, u.balance, u.currency, u.is_banned, u.created_at, u.updated_at, u.parent_id,
            JSON_ARRAY_APPEND(utd.path, '$', u.id), -- Append current ID to path
            utd.level + 1
          FROM users u
          INNER JOIN UserTreeData utd ON u.parent_id = utd.id
        )
        SELECT id, username, role, balance, currency, is_banned, created_at, updated_at, parent_id
        FROM UserTreeData
        ORDER BY path; -- Order by path to facilitate tree building
      `;

      const usersData = await sequelize.query<any>(treeQuery, {
        replacements: { targetUserId },
        type: QueryTypes.SELECT,
      });

      if (!usersData || usersData.length === 0) {
        return res.status(404).json({
          status: 'error',
          message: 'User tree not found or user does not exist'
        });
      }

      // Build the tree structure from the flat list ordered by path
      const userMap = new Map<number, UserTree>();
      let rootNode: UserTree | null = null;

      for (const userData of usersData) {
        const node: UserTree = {
          id: userData.id,
          username: userData.username,
          role: userData.role,
          balance: userData.balance,
          currency: userData.currency,
          is_banned: userData.is_banned === 1 ? true : false,
          created_at: userData.created_at,
          updated_at: userData.updated_at,
          children: []
        };
        userMap.set(node.id, node);

        if (userData.id === targetUserId) {
          rootNode = node;
        } else if (userData.parent_id) {
          const parent = userMap.get(userData.parent_id);
          if (parent) {
            parent.children.push(node);
          }
        }
      }

      if (!rootNode) {
         // Should not happen if usersData is not empty, but good safety check
         return res.status(404).json({ status: 'error', message: 'Root node not found in tree data' });
      }

      return res.json({
        status: 'success',
        data: rootNode
      });

    } catch (error) {
      console.error('Get user tree error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Failed to get user tree'
      });
    }
  }

}
