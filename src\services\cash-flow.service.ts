import { CashFlow } from '../models/cash-flow.model';
import User, { UserRole } from '../models/user.model';
import { Transaction, Op, QueryTypes } from 'sequelize'; // Added QueryTypes
import sequelize from '../config/database';
import { ForbiddenError, NotFoundError } from '../utils/errors';
import { UserService } from './user.service'; // Added UserService

interface RoleHierarchy {
  [key: string]: number;
}

interface PaginationParams {
  page?: number;
  limit?: number;
  startDate?: Date;
  endDate?: Date;
  type?: 'add' | 'deduct';
}

interface PaginatedResult<T> {
  history: T[];
  total: number;
}

export class CashFlowService {




  static async addCash(fromUserId: number, toUserId: number, amount: number): Promise<void> {
    return sequelize.transaction(async (t: Transaction) => {
      const [fromUser, toUser] = await Promise.all([
        User.findByPk(fromUserId),
        User.findByPk(toUserId),
      ]);

      if (!fromUser || !toUser) {
        throw new NotFoundError('User not found');
      }

      // Check if fromU<PERSON> is the direct parent of toUser


      if ( toUser.parent_id !== fromUser.id) {
        throw new ForbiddenError('Unauthorized: You can only add cash to your direct children');
      }

      // Check if fromUser has sufficient balance to add
      if (fromUser.balance < amount) {
        throw new Error('Insufficient balance: You do not have enough funds to perform this operation');
      }

      // Create cash flow record
      await CashFlow.create({
        fromUserId,
        toUserId,
        amount,
        type: 'add',
      }, { transaction: t });

      // Update balances
      await Promise.all([
        fromUser.decrement('balance', { by: amount, transaction: t }),
        toUser.increment('balance', { by: amount, transaction: t })
      ]);
    });
  }

  static async deductCash(fromUserId: number, toUserId: number, amount: number): Promise<void> {
    return sequelize.transaction(async (t: Transaction) => {
      const [fromUser, toUser] = await Promise.all([
        User.findByPk(fromUserId),
        User.findByPk(toUserId),
      ]);

      if (!fromUser || !toUser) {
        throw new NotFoundError('User not found');
      }

      // Check if fromUser is the direct parent of toUser
      if ( toUser.parent_id !== fromUser.id) {
        throw new ForbiddenError('Unauthorized: You can only deduct cash from your direct children');
      }

      // Check if toUser has sufficient balance to be deducted
      if (toUser.balance < amount) {
        throw new Error('Insufficient balance: The target user does not have enough funds to be deducted');
      }

      // Create cash flow record
      await CashFlow.create({
        fromUserId,
        toUserId,
        amount,
        type: 'deduct',
      }, { transaction: t });

      // Update balances
      await Promise.all([
        fromUser.increment('balance', { by: amount, transaction: t }),
        toUser.decrement('balance', { by: amount, transaction: t })
      ]);
    });
  }





  static async getCashFlowHistory(
    viewer: User,
    targetUser: User,
    params: PaginationParams
  ): Promise<PaginatedResult<CashFlow>> {
    if (!viewer || !targetUser) {
      throw new NotFoundError('User not found');
    }

    // Check if viewer has permission to view target user's transactions
    if (viewer.role === UserRole.PLAYER) {
      // Players can only view their own transactions or transactions with their direct parent
      if (targetUser.id !== viewer.id && targetUser.id !== viewer.parent_id) {
        throw new ForbiddenError('Unauthorized to view this history');
      }
    } else {
      // For all other roles, check if target is in viewer's hierarchy
      const descendantIds = await UserService.getDescendantIds(viewer.id);
      const allowedUserIds = [
        viewer.id,
        ...(viewer.parent_id ? [viewer.parent_id] : []),
        ...descendantIds
      ];

      if (!allowedUserIds.includes(targetUser.id)) {
        throw new ForbiddenError('Unauthorized to view this history');
      }
    }

    // Get all relevant user IDs (target user + descendants)
    const descendantIds = await UserService.getDescendantIds(targetUser.id);
    const allUserIds = [targetUser.id, ...descendantIds];

    // Build optimized where clause
    const whereClause: any = {
      [Op.or]: [
        { fromUserId: { [Op.in]: allUserIds } },
        { toUserId: { [Op.in]: allUserIds } }
      ]
    };

    // Add date filters if provided
    this.buildDateFilterClause(params, whereClause);

    return this.queryCashFlowHistory(whereClause, params);
  }

  static async getAllHistory(
    viewerId: number,
    params: PaginationParams
  ): Promise<PaginatedResult<CashFlow>> {
    const viewer = await User.findByPk(viewerId);

    if (!viewer) {
      throw new NotFoundError('User not found');
    }




    // Build optimized where clause
    const whereClause: any = {
      [Op.or]: [
        { fromUserId: { [Op.in]: [viewerId] } },
        { toUserId: { [Op.in]: [viewerId] } }
      ]
    };

    // Add date filtering if provided
    this.buildDateFilterClause(params, whereClause);

    return this.queryCashFlowHistory(whereClause, params);
  }

  private static buildDateFilterClause(params: PaginationParams, whereClause: any = {}): any {
    // Add date filters if provided
    if (params.startDate || params.endDate) {
      whereClause.createdAt = {};
      if (params.startDate) whereClause.createdAt[Op.gte] = params.startDate;
      if (params.endDate) whereClause.createdAt[Op.lte] = params.endDate;
    }

    // Add transaction type filter if provided
    if (params.type) {
      whereClause.type = params.type;
    }

    return whereClause;
  }

  private static async queryCashFlowHistory(whereClause: any, params: PaginationParams): Promise<PaginatedResult<CashFlow>> {
    const { count, rows } = await CashFlow.findAndCountAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
      limit: params.limit,
      offset: ((params.page || 1) - 1) * (params.limit || 20),
      include: [
        {
          model: User,
          as: 'fromUser',
          attributes: ['id', 'username', 'role']
        },
        {
          model: User,
          as: 'toUser',
          attributes: ['id', 'username', 'role']
        }
      ]
    });

    return {
      history: rows,
      total: count
    };
  }

}
