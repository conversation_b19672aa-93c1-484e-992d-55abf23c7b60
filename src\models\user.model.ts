import { Model, DataTypes } from 'sequelize';
import bcrypt from 'bcryptjs';
import sequelize from '../config/database';

export enum UserRole {
  SUPEROWNER = 'superowner',
  OWNER = 'owner', 
  SUPERADMIN = 'superadmin',
  ADMIN = 'admin',
  CASHIER = 'cashier',
  PLAYER = 'player'
}

export interface UserAttributes {
  id: number;
  username: string;
  password: string;
  role: UserRole;
  balance: number;
  parent_id?: number;
  currency: string;
  is_active: boolean;
  is_banned: boolean;
  last_login?: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface UserCreationAttributes extends Omit<UserAttributes, 'id'> {}

class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: number;
  public username!: string;
  public password!: string;
  public role!: UserRole;
  public balance!: number;
  public parent_id?: number;
  public currency!: string;
  public is_active!: boolean;
  public is_banned!: boolean;
  public last_login?: Date;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Hash password before saving
  public static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }

  // Verify password
  public async verifyPassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }
}

User.init(
  {
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
        is: /^[a-zA-Z0-9_]+$/,
        len: [5, 20]
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    role: {
      type: DataTypes.ENUM(...Object.values(UserRole)),
      allowNull: false,
    },
    balance: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
    parent_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: true,
    },
    currency: {
      type: DataTypes.STRING(3),
      defaultValue: 'TND',
    },
    is_active: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    is_banned: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: 'users',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['username']
      }
    ],
    hooks: {
      beforeCreate: async (user: User) => {
        // Validate username format
        if (!/^[a-zA-Z0-9_]+$/.test(user.username)) {
          throw new Error('Username can only contain letters, numbers, and underscores');
        }
        // Validate username length
        if (user.username.length < 5 || user.username.length > 20) {
          throw new Error('Username must be between 5 and 20 characters');
        }
        if (user.password) {
          user.password = await User.hashPassword(user.password);
        }
      },
      beforeUpdate: async (user: User) => {
        // Prevent username updates
        if (user.changed('username')) {
          throw new Error('Username cannot be updated');
        }
        // Only hash password if it's being changed
        if (user.changed('password')) {
          user.password = await User.hashPassword(user.password);
        }
      }
    }
  }
);

// Self-referential relationship for hierarchy
User.belongsTo(User, { as: 'parent', foreignKey: 'parent_id' });
User.hasMany(User, { as: 'children', foreignKey: 'parent_id' });

export default User;
