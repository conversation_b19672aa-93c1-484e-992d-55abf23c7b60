import { Sequelize, QueryTypes } from 'sequelize';
import dotenv from 'dotenv';
import bcrypt from 'bcrypt';
import mysql from 'mysql2/promise';

dotenv.config();

// Get database configuration from environment variables
const dbHost = process.env.DB_HOST || 'localhost';
const dbPort = parseInt(process.env.DB_PORT || '3306');
const dbName = process.env.DB_NAME || 'casino_db';
const dbUser = process.env.DB_USER || 'root';
const dbPassword = process.env.DB_PASSWORD || '';
const isDevelopment = process.env.NODE_ENV !== 'production';

// Create Sequelize instance
const sequelize = new Sequelize({
  dialect: 'mysql',
  host: dbHost,
  port: dbPort,
  username: dbUser,
  password: dbPassword,
  database: dbName,
  logging: isDevelopment ? console.log : false,
  pool: {
    max: 5,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
});

/**
 * Create the database if it doesn't exist
 */
async function createDatabaseIfNotExists(): Promise<void> {
  try {
    // Create a connection without specifying a database
    const connection = await mysql.createConnection({
      host: dbHost,
      port: dbPort,
      user: dbUser,
      password: dbPassword,
    });

    // Create the database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\`;`);
    console.log(`Ensured database '${dbName}' exists`);

    // Close the connection
    await connection.end();
  } catch (error) {
    console.error('Error creating database:', error);
    throw error;
  }
}

/**
 * Initialize the database
 */
export const initDatabase = async (): Promise<void> => {
  try {
    // First, ensure the database exists
    await createDatabaseIfNotExists();

    // Try to authenticate the connection
    try {
      await sequelize.authenticate();
      console.log('Database connection authenticated successfully');
    } catch (authError) {
      console.error('Authentication error:', authError);
      throw authError;
    }

    // Check if tables exist by trying to find the users table
    const tablesExist = await sequelize.query(
      `SELECT table_name
       FROM information_schema.tables
       WHERE table_schema = '${dbName}' AND table_name = 'users'`,
      { type: QueryTypes.SELECT }
    );

    if (tablesExist.length === 0) {
      try {
        // Tables don't exist, create them using the models
        // This will create all tables defined in the models
        // Use force: false to avoid dropping existing tables
        await sequelize.sync({ force: false });
        console.log('Database tables created successfully');

        // Create superowner user with hashed password
        const hashedPassword = await bcrypt.hash('superowner123!', 10);

        // Create owner (id: 1)
        await sequelize.query(`
          INSERT INTO users (
            id,
            username,
            password,
            role,
            currency,
            balance,
            is_active,
            is_banned,
            parent_id,
            created_at,
            updated_at
          ) VALUES (
            1,
            'superowner',
            '${hashedPassword}',
            'superowner',
            'TND',
            1000000,
            true,
            false,
            NULL,
            NOW(),
            NOW()
          );
        `);
        console.log('Superowner created successfully');
      } catch (syncError: any) {
        console.error('Error creating database tables:', syncError);
        if (syncError.name === 'SequelizeDatabaseError') {
          console.error('Database error details:', syncError.parent?.message || 'Unknown error');
        }
        throw syncError;
      }
    } else {
      console.log('Database tables already exist');
    }
  } catch (error) {
    console.error('Unable to initialize database:', error);
    throw error;
  }
};

export default sequelize;
