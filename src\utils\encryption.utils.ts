import CryptoJS from 'crypto-js';

export class EncryptionUtils {
  private static readonly ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-encryption-key';

  /**
   * Encrypts sensitive data using AES-256
   * @param data Data to encrypt
   * @returns Encrypted data as string
   */
  static encrypt(data: string): string {
    return CryptoJS.AES.encrypt(data, this.ENCRYPTION_KEY).toString();
  }

  /**
   * Decrypts AES-256 encrypted data
   * @param encryptedData Encrypted data string
   * @returns Decrypted data
   */
  static decrypt(encryptedData: string): string {
    const bytes = CryptoJS.AES.decrypt(encryptedData, this.ENCRYPTION_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  }

  /**
   * Encrypts an object by converting it to JSON and encrypting the result
   * @param data Object to encrypt
   * @returns Encrypted string
   */
  static encryptObject<T>(data: T): string {
    const jsonString = JSON.stringify(data);
    return this.encrypt(jsonString);
  }

  /**
   * Decrypts an encrypted string and parses it back to an object
   * @param encryptedData Encrypted string
   * @returns Decrypted object
   */
  static decryptObject<T>(encryptedData: string): T {
    const decryptedString = this.decrypt(encryptedData);
    return JSON.parse(decryptedString);
  }
}
