import { Request, Response, NextFunction } from 'express';
import { RoleService } from '../services/role.service';
import { UserService } from '../services/user.service';
import { UserRole } from '../models/user.model';
import User from '../models/user.model';
import { Op } from 'sequelize';

export class RoleMiddleware {
  // Middleware to check if user can manage target user
  static canManageUser = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    try {
      const managerId = req.user?.id;
      const targetUserId = parseInt(req.params.userId);

      if (!managerId) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Allow users to manage their own profile
      if (managerId === targetUserId) {
        return next();
      }

      const [manager, targetUser] = await Promise.all([
        User.findByPk(managerId),
        User.findByPk(targetUserId),
      ]);

      if (!manager || !targetUser) {
        return res.status(404).json({ message: 'User not found' });
      }

      if (!RoleService.canManageRole(manager.role, targetUser.role)) {
        return res.status(403).json({ message: 'Insufficient permissions to manage this user' });
      }

      next();
    } catch (error) {
      console.error('Role management error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };

  // Middleware to check if user has one of the allowed roles
  static hasRole = (allowedRoles: UserRole[]) => {
    return (req: Request, res: Response, next: NextFunction): Response | void => {
      const userRole = req.user?.role as UserRole;

      if (!userRole || !allowedRoles.includes(userRole)) {
        return res.status(403).json({ message: 'Insufficient permissions' });
      }

      next();
    };
  };

  // Middleware to check specific permissions
  static hasPermission = (permission: string) => {
    return (req: Request, res: Response, next: NextFunction): Response | void => {
      const userRole = req.user?.role as UserRole;

      if (!userRole || !RoleService.hasPermission(userRole, permission)) {
        return res.status(403).json({ message: 'Insufficient permissions' });
      }

      next();
    };
  };

  // Middleware to validate role hierarchy in user creation
  static validateHierarchy = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    try {
      const managerId = req.user?.id;
      const targetRole = req.body.role as UserRole;

      if (!managerId) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const manager = await User.findByPk(managerId);
      if (!manager) {
        return res.status(404).json({ message: 'Manager not found' });
      }

      if (!RoleService.canManageRole(manager.role, targetRole)) {
        return res.status(403).json({ message: 'Cannot create user with this role' });
      }

      next();
    } catch (error) {
      console.error('Role hierarchy validation error:', error);
      return res.status(500).json({ message: 'Internal server error' });
    }
  };

  static canCreateRole(req: Request, res: Response, next: NextFunction): void {
    const creatorRole = req.user?.role;
    const targetRole = req.body.role as UserRole;

    // Strict role hierarchy
    if (targetRole === UserRole.OWNER && creatorRole === UserRole.SUPEROWNER) {
      next();
      return;
    }

    if (targetRole === UserRole.SUPERADMIN && creatorRole === UserRole.SUPEROWNER) {
      next();
      return;
    }

    if (targetRole === UserRole.SUPERADMIN && creatorRole === UserRole.OWNER) {
      next();
      return;
    }

    if (targetRole === UserRole.ADMIN && creatorRole === UserRole.SUPERADMIN) {
      next();
      return;
    }

    if (targetRole === UserRole.CASHIER && creatorRole === UserRole.ADMIN) {
      next();
      return;
    }

    if (targetRole === UserRole.PLAYER && creatorRole === UserRole.CASHIER) {
      next();
      return;
    }

    res.status(403).json({
      status: 'error',
      message: `${creatorRole} cannot create ${targetRole}`
    });
  }

  static canViewTree = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<Response | void> => {
    try {
      const callerId = req.user!.id;
      const targetUserId = req.body.user_id;

      // Single query to get both users
      const users = await User.findAll({
        where: { id: { [Op.in]: [callerId, targetUserId] } },
        attributes: ['id', 'role', 'parent_id']
      });

      const caller = users.find(u => u.id === callerId);
      const targetUser = users.find(u => u.id === targetUserId);

      if (!caller || !targetUser) {
        return res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
      }

      // Players can only view their own tree
      if (caller.role === UserRole.PLAYER && callerId !== targetUserId) {
        return res.status(403).json({
          status: 'error',
          message: 'Players can only view their own tree'
        });
      }

      // For non-players, check hierarchy using the optimized service method
      if (caller.role !== UserRole.PLAYER) {
        const isAllowed = await RoleService.isInHierarchy(callerId, targetUserId);
        if (!isAllowed) {
          return res.status(403).json({
            status: 'error',
            message: 'Unauthorized access to view this user\'s tree'
          });
        }
      }

      next(); // Allow access if checks pass
    } catch (error) {
      console.error('Tree access validation error:', error);
      return res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  };

  static async canBanUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const managerRole = req.user?.role;
      const managerId = req.user?.id;
      const targetUserId = parseInt(req.body.user_id);

      if (!managerId) {
        res.status(401).json({
          status: 'error',
          message: 'Authentication required'
        });
        return;
      }

      if (isNaN(targetUserId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid user ID'
        });
        return;
      }

      const targetUser = await User.findByPk(targetUserId, {
        attributes: ['id', 'role', 'parent_id']
      });

      if (!targetUser) {
        res.status(404).json({
          status: 'error',
          message: 'Target user not found'
        });
        return;
      }

      // SUPEROWNER can ban anyone
      if (managerRole === UserRole.SUPEROWNER) {
        next();
        return;
      }

      // OWNER can ban anyone below them
      if (managerRole === UserRole.OWNER) {
        if (RoleService.canManageRole(UserRole.OWNER, targetUser.role)) {
          next();
          return;
        }
        res.status(403).json({
          status: 'error',
          message: 'Owner can only ban users in their hierarchy'
        });
        return;
      }

      // SUPERADMIN can ban ADMIN, CASHIER, and PLAYER in their hierarchy
      if (managerRole === UserRole.SUPERADMIN) {
        const descendants = await UserService.getDescendantIds(managerId);
        if (descendants.includes(targetUserId) && RoleService.canManageRole(UserRole.SUPERADMIN, targetUser.role)) {
          next();
          return;
        }
        res.status(403).json({
          status: 'error',
          message: 'Superadmin can only ban users in their hierarchy'
        });
        return;
      }

      // ADMIN can ban CASHIER and PLAYER in their hierarchy
      if (managerRole === UserRole.ADMIN) {
        const descendants = await UserService.getDescendantIds(managerId);
        if (descendants.includes(targetUserId) && RoleService.canManageRole(UserRole.ADMIN, targetUser.role)) {
          next();
          return;
        }
        res.status(403).json({
          status: 'error',
          message: 'Admin can only ban users in their hierarchy'
        });
        return;
      }

      // CASHIER can only ban their own players
      if (managerRole === UserRole.CASHIER) {
        if (targetUser.role === UserRole.PLAYER && targetUser.parent_id === managerId) {
          next();
          return;
        }
        res.status(403).json({
          status: 'error',
          message: 'Cashier can only ban their own players'
        });
        return;
      }

      res.status(403).json({
        status: 'error',
        message: 'Insufficient permissions to ban/unban users'
      });
    } catch (error) {
      console.error('Ban permission check error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
}
