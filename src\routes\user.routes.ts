import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { UserSearchController } from '../controllers/user-search.controller';
import { authenticate } from '../middleware/auth.middleware';
import { csrfProtection } from '../middleware/csrf.middleware';
import { RoleMiddleware } from '../middleware/role.middleware';
import { UserRole } from '../models/user.model';
import { validate } from '../middleware/validation.middleware';
import { body as _body } from 'express-validator/src/middlewares/validation-chain-builders';
import { statsCache } from '../utils/cache.utils';
import User from '../models/user.model';
const router = Router();
const body = _body;

// Public routes with validation
router.post(
  '/login',
  validate([
    body('username').trim().notEmpty().withMessage('Username is required'),
    body('password').trim().notEmpty().withMessage('Password is required')
  ]),
  UserController.login
);

router.post(
  '/refresh-token',
  UserController.refreshToken
);

router.post(
  '/logout',
  UserController.logout
);

// Protected routes
router.use(authenticate);

// Apply CSRF protection to all routes that modify data
router.use(csrfProtection);

// Create user (cashiers only)
router.post(
  '/create',
  validate([
    body('username')
      .trim()
      .matches(/^[a-zA-Z0-9_]+$/).withMessage('Username can only contain letters, numbers, and underscores')
      .isLength({ min: 5, max: 20 }).withMessage('Username must be between 5 and 20 characters'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('Password must be at least 8 characters')
      .matches(/^(?=.*[a-z])(?=.*\d)/)
      .withMessage('Password must contain at least one lowercase letter, and one number'),
    body('role').isIn(Object.values(UserRole)).withMessage('Invalid role'),
    body('currency').optional().isLength({ min: 3, max: 3 }).withMessage('Currency must be 3 characters')
  ]),
  RoleMiddleware.canCreateRole,
  UserController.createUser
);

// Ban user
router.post(
  '/ban',
  validate([
    body('user_id').isInt().withMessage('User ID must be an integer')
  ]),
  RoleMiddleware.canBanUser,
  UserController.banUser
);

// Unban user
router.post(
  '/unban',
  validate([
    body('user_id').isInt().withMessage('User ID must be an integer')
  ]),
  RoleMiddleware.canBanUser,
  UserController.unbanUser
);

// Advanced search route
router.get('/search',
  validate([
    body('username').optional().trim().escape()
      .matches(/^[a-zA-Z0-9_]*$/).withMessage('Username can only contain letters, numbers, and underscores'),
    body('roles').optional()
      .custom((value: string) => {
        if (value) {
          const roles = value.split(',');
          return roles.every((role: string) => Object.values(UserRole).includes(role as UserRole));
        }
        return true;
      }).withMessage('Invalid role(s) specified'),
    body('isBanned').optional().isBoolean().toBoolean(),
    body('isActive').optional().isBoolean().toBoolean(),
    body('startDate').optional().isISO8601().toDate(),
    body('endDate').optional().isISO8601().toDate()
      .custom((endDate: Date, { req }) => {
        if (req.body.startDate && endDate) {
          return new Date(endDate) >= new Date(req.body.startDate);
        }
        return true;
      }).withMessage('End date must be after start date'),
    body('page').optional().isInt({ min: 1 }).toInt(),
    body('level').optional().isInt({ min: 1, max: 2 }).toInt(),
    body('limit').optional().isInt({ min: 1, max: 100 }).toInt()
  ]),
  RoleMiddleware.hasPermission('search_users'),
  UserSearchController.searchUsers
);

// Get user profile details
router.get(
  '/details',
  authenticate,
  UserController.getProfile
);

// Get specific user profile details (for admins/cashiers)
router.post(
  '/details',
  authenticate,
  validate([
    body('user_id').isInt().withMessage('User ID must be an integer')
  ]),
  UserController.getProfile
);

// Change password with role-based access
router.post(
  '/password',
  validate([
    body('user_id').isInt().withMessage('User ID must be an integer'),
    body('old_password').optional(),
    body('new_password')
      .isLength({ min: 8 })
      .withMessage('New password must be at least 8 characters')
      .matches(/^(?=.*[a-z])(?=.*\d)/)
      .withMessage('Password must contain at least one lowercase letter, and one number'),
    body('retype_password')
      .custom((value, { req }) => {
        if (value !== req.body.new_password) {
          throw new Error('Passwords do not match');
        }
        return true;
      })
  ]),
  UserController.changePassword
);


// Admin dashboard stats
router.get(
  '/stats',
  authenticate,
  RoleMiddleware.hasRole([UserRole.SUPEROWNER,UserRole.OWNER, UserRole.SUPERADMIN,UserRole.ADMIN, UserRole.CASHIER]),
  async (req, res) => {
    const cacheKey = `stats_${req.user!.id}`;
    const cachedStats = statsCache.get(cacheKey);

    if (cachedStats) {
      return res.json({
        status: 'success',
        data: cachedStats,
        cached: true
      });
    }

    const stats = await UserController.getStats(req.user!.id);
    statsCache.set(cacheKey, stats, 300); // Cache for 5 minutes

    return res.json({
      status: 'success',
      data: stats,
      cached: false
    });
  }
);

// User tree route
router.post(
  '/tree',
  authenticate,
  RoleMiddleware.hasRole([UserRole.SUPEROWNER,UserRole.OWNER, UserRole.SUPERADMIN,UserRole.ADMIN, UserRole.CASHIER]),
  validate([
    body('user_id')
      .isInt().withMessage('User ID must be an integer')
      .toInt()
      .custom(async (value, { req }) => {
        const user = await User.findByPk(value);
        if (!user) {
          throw new Error('User not found');
        }
        return true;
      })
  ]),
  RoleMiddleware.canViewTree,
  UserController.getUserTree
);

export default router;
