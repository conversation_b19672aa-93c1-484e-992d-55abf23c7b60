import { Request, Response } from 'express';
import { GameService } from '../services/game.service';

export class GameHistoryController {
  static async getHistory(req: Request, res: Response): Promise<void> {
    try {
      const viewerId = req.user!.id;
      
      // Get pagination and date filter params from query
      const params = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined
      };

      const { history, total } = await GameService.getGameHistory(viewerId, params);
      
      const transformedHistory = history.map(transaction => ({
        id: transaction.id,
        gameId: transaction.gameId,
        name: transaction.name,
        bet: transaction.bet,
        win: transaction.win,
        action: transaction.action,
        date: transaction.createdAt,
        balance_before: transaction.balance_before,
        balance_after: transaction.balance_after,
        user: transaction.user || {
          id: transaction.userId,
          username: 'Unknown'
        }
      }));

      res.status(200).json({
        status: 'success',
        data: transformedHistory,
        pagination: {
          page: params.page || 1,
          limit: params.limit || 20,
          total,
          totalPages: Math.ceil(total / (params.limit || 20))
        }
      });
    } catch (error: any) {
      if (error.message.includes('User not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('Invalid role')) {
        res.status(403).json({
          status: 'error',
          message: 'Unauthorized to view game history'
        });
      } else {
        console.error('Error getting game history:', error);
        res.status(500).json({
          status: 'error',
          message: 'Internal server error'
        });
      }
    }
  }

  static async getHistoryByUserId(req: Request, res: Response): Promise<void> {
    try {
      const viewerId = req.user!.id;
      const targetUserId = parseInt(req.params.userId);
      
      if (isNaN(targetUserId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid user ID'
        });
        return;
      }

      // Get pagination and date filter params from query
      const params = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined
      };

      const { history, total } = await GameService.getGameHistoryByUserId(viewerId, targetUserId, params);
      
      const transformedHistory = history.map(transaction => ({
        id: transaction.id,
        gameId: transaction.gameId,
        name: transaction.name,
        bet: transaction.bet,
        win: transaction.win,
        action: transaction.action,
        date: transaction.createdAt,
        balance_before: transaction.balance_before,
        balance_after: transaction.balance_after,
        user: transaction.user || {
          id: transaction.userId,
          username: 'Unknown'
        }
      }));

      res.status(200).json({
        status: 'success',
        data: transformedHistory,
        pagination: {
          page: params.page || 1,
          limit: params.limit || 20,
          total,
          totalPages: Math.ceil(total / (params.limit || 20))
        }
      });
    } catch (error: any) {
      if (error.message.includes('User not found') || error.message.includes('Target user not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else {
        console.error('Error in getHistoryByUserId:', error);
        res.status(500).json({
          status: 'error',
          message: 'Internal server error'
        });
      }
    }
  }
}
