import { UserRole } from '../models/user.model';
import User from '../models/user.model';
import sequelize from '../config/database'; // Added import
import { QueryTypes } from 'sequelize'; // Added import

export class RoleService {
  // Role hierarchy definition
  private static readonly roleHierarchy: { [key in UserRole]?: UserRole[] } = {
    [UserRole.SUPEROWNER]: [UserRole.OWNER, UserRole.SUPERADMIN, UserRole.ADMIN, UserRole.CASHIER, UserRole.PLAYER],
    [UserRole.OWNER]: [UserRole.SUPERADMIN, UserRole.ADMIN, UserRole.CASHIER, UserRole.PLAYER],
    [UserRole.SUPERADMIN]: [UserRole.ADMIN, UserRole.CASHIER, UserRole.PLAYER],
    [UserRole.ADMIN]: [UserRole.CASHIER, UserRole.PLAYER],
    [UserRole.CASHIER]: [UserRole.PLAYER],
    [UserRole.PLAYER]: [],
  };

  // Permission sets for each role
  private static readonly rolePermissions: { [key in UserRole]: string[] } = {
    [UserRole.SUPEROWNER]: [
      'manage_all_users',
      'manage_superadmins',
      'manage_cashiers', 
      'manage_players',
      'view_all_transactions',
      'manage_system',
      'search_users',
      'initialize_database'
    ],
    [UserRole.OWNER]: [
      'manage_all_users',
      'manage_superadmins',
      'manage_cashiers',
      'manage_players',
      'view_all_transactions',
      'manage_system',
      'search_users'
    ],
    [UserRole.ADMIN]: [
      'manage_cashiers',
      'manage_players',
      'view_subordinate_transactions',
      'search_users'
    ],
    [UserRole.SUPERADMIN]: [
      'manage_cashiers',
      'manage_players',
      'view_subordinate_transactions',
      'search_users'
    ],
    [UserRole.CASHIER]: [
      'manage_players',
      'view_player_transactions',
      'search_users'
    ],
    [UserRole.PLAYER]: [
      'play_games',
      'view_own_transactions'
    ],
  };

  // Check if a role can manage another role
  static canManageRole(managerRole: UserRole, targetRole: UserRole): boolean {
    if (managerRole === targetRole) return false;
    return this.roleHierarchy[managerRole]?.includes(targetRole) || false;
  }

  // Get all roles that can be managed by a given role
  static getManageableRoles(role: UserRole): UserRole[] {
    return this.roleHierarchy[role] || [];
  }

  // Check if a role has a specific permission
  static hasPermission(role: UserRole, permission: string): boolean {
    return this.rolePermissions[role]?.includes(permission) || false;
  }

  /**
   * Check if a user can manage another user based on their roles
   * This is similar to canManageRole but adds additional safety checks
   */
  static canManageUser(managerRole: UserRole, targetRole: UserRole): boolean {
    // Cannot manage yourself or higher roles
    if (managerRole === targetRole) return false;
    
    // Check role hierarchy
    const manageableRoles = this.roleHierarchy[managerRole] || [];
    return manageableRoles.includes(targetRole);
  }

  // Get all permissions for a role
  static getRolePermissions(role: UserRole): string[] {
    return this.rolePermissions[role] || [];
  }

  // Check if one role is superior to another in hierarchy
  static isSuperiorTo(role1: UserRole, role2: UserRole): boolean {
    const subordinates = this.getAllSubordinateRoles(role1);
    return subordinates.includes(role2);
  }

  // Get all subordinate roles (recursive)
  static getAllSubordinateRoles(role: UserRole): UserRole[] {
    const directSubordinates = this.roleHierarchy[role] || [];
    const allSubordinates = [...directSubordinates];

    directSubordinates.forEach(subordinateRole => {
      const lowerSubordinates = this.getAllSubordinateRoles(subordinateRole);
      allSubordinates.push(...lowerSubordinates);
    });

    return [...new Set(allSubordinates)];
  }

  // Validate parent-child relationship
  static isValidParentChild(parentRole: UserRole, childRole: UserRole): boolean {
    return this.isSuperiorTo(parentRole, childRole);
  }

  /**
   * Check if target user is in manager's hierarchy
   * @param managerId - ID of the manager user
   * @param targetUserId - ID of the target user to check
   * @returns Promise<boolean> - true if target is in manager's hierarchy
   */
  static async isInHierarchy(managerId: number, targetUserId: number): Promise<boolean> {
    // A user is always in their own hierarchy
    if (managerId === targetUserId) return true;

    const query = `
      WITH RECURSIVE Ancestors AS (
        -- Anchor member: Select the target user
        SELECT id, parent_id
        FROM users
        WHERE id = :targetUserId
        UNION ALL
        -- Recursive member: Select the parent of the previous level
        SELECT u.id, u.parent_id
        FROM users u
        INNER JOIN Ancestors a ON u.id = a.parent_id
        WHERE a.parent_id IS NOT NULL -- Stop when parent_id is null
      )
      SELECT 1 FROM Ancestors WHERE id = :managerId LIMIT 1;
    `;

    try {
      const results = await sequelize.query(query, {
        replacements: { targetUserId, managerId },
        type: QueryTypes.SELECT,
      });
      // If the managerId is found in the ancestors, the result array will not be empty
      return results.length > 0;
    } catch (error) {
      console.error('Error checking hierarchy:', error);
      throw new Error('Failed to check user hierarchy');
    }
  }

  static async canViewProfile(viewerId: number, viewerRole: UserRole, targetUserId: number): Promise<boolean> {
    // A user can always view their own profile
    if (viewerId === targetUserId) return true;

    // Check if target is in viewer's hierarchy
    const isInHierarchy = await this.isInHierarchy(viewerId, targetUserId);
    
    // Get target's role
    const targetUser = await User.findByPk(targetUserId, { attributes: ['role'] });
    const targetRole = targetUser?.role || null;

    // SUPEROWNER can view anyone in their hierarchy
    if (viewerRole === UserRole.SUPEROWNER && isInHierarchy) {
      return true;
    }

    // For other roles, check hierarchy and role management permissions
    return isInHierarchy && targetRole ? this.canManageRole(viewerRole, targetRole) : false;
  }
}
