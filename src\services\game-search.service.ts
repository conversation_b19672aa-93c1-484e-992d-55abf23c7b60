import { Op, QueryTypes } from 'sequelize';
import Game from '../models/game.model';
import sequelize from '../config/database'; // Import sequelize instance
import { gameCache, CACHE_KEYS } from '../utils/cache.utils';

interface GameSearchFilters {
  name?: string;
  title?: string;
  categories?: string[];
  device?: string;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'title';
  sortOrder?: 'ASC' | 'DESC';
}

interface PaginatedResponse {
  games: Game[];
  pagination: {
    total: number;
    page: number;
    totalPages: number;
    limit: number;
  };
}

type WhereClauseType = {
  [key: string]: any;
};

export class GameSearchService {
  private static readonly DEFAULT_LIMIT = 20;
  private static readonly MAX_LIMIT = 100;
  private static readonly ALLOWED_SORT_COLUMNS = ['name', 'title']; // Whitelist for sorting

  static async searchGames(filters: GameSearchFilters): Promise<PaginatedResponse> {
    try {
      const {
        name,
        title,
        categories,
        device,
        page = 1,
        limit = this.DEFAULT_LIMIT,
        sortBy = 'name', // Default sort column
        sortOrder = 'ASC' // Default sort order
      } = filters;

      // Sanitize and validate input
      const sanitizedLimit = Math.min(Math.max(1, limit), this.MAX_LIMIT);
      const sanitizedPage = Math.max(1, page);
      const offset = (sanitizedPage - 1) * sanitizedLimit;

      // Validate sort parameters against whitelist
      const validSortBy = this.ALLOWED_SORT_COLUMNS.includes(sortBy) ? sortBy : 'name';
      const validSortOrder = sortOrder.toUpperCase() === 'DESC' ? 'DESC' : 'ASC';

      // Build where conditions
      const where: any = {};

      if (name) {
        where.name = { [Op.like]: `%${name}%` };
      }

      if (title) {
        where.title = { [Op.like]: `%${title}%` };
      }

      if (device) {
        where.device = device;
      }

      if (categories && categories.length > 0) {
        where[Op.or] = categories.map(cat => ({
          categories: { [Op.like]: `%${cat}%` }
        }));
      }

      // Single query using findAndCountAll for better performance
      const { count, rows } = await Game.findAndCountAll({
        where,
        limit: sanitizedLimit,
        offset: offset,
        order: [[validSortBy, validSortOrder]],
        raw: true
      });

      const games = rows as unknown as Game[];
      const total = count;

      return {
        games: games,
        pagination: {
          total: total,
          page: sanitizedPage,
          totalPages: Math.ceil(total / sanitizedLimit),
          limit: sanitizedLimit
        }
      };
    } catch (error) {
      console.error('Game search error:', error);
      throw error;
    }
  }

  static async getProvCate(type: 'categories' | 'titles'): Promise<string[]> {
    try {
      const cacheKey = type === 'categories' ? CACHE_KEYS.CATEGORIES : CACHE_KEYS.TITLES;
      
      // Try to get from cache first
      const cachedData = gameCache.get<string[]>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      // If not in cache, get from database
      const data = type === 'categories' 
        ? await Game.getDistinctCategories()
        : await Game.getDistinctTitles();

      // Store in cache with 2 hour TTL
      gameCache.set(cacheKey, data, 7200);
      
      return data;
    } catch (error) {
      console.error('Error getting prov-cate:', error);
      throw error;
    }
  }

  static validateSearchParams(params: Record<string, unknown>): GameSearchFilters {
    const filters: GameSearchFilters = {};

    // Validate and sanitize name
    if (typeof params.name === 'string') {
      filters.name = params.name.trim().slice(0, 100);
    }

    // Validate and sanitize title
    if (typeof params.title === 'string') {
      filters.title = params.title.trim().slice(0, 100);
    }

    // Validate and sanitize categories
    if (typeof params.categories === 'string') {
      // Split by comma and trim each category
      filters.categories = params.categories
        .split(',')
        .map(cat => cat.trim())
        .filter(cat => cat.length > 0);
    }

    // Validate device
    if (typeof params.device === 'string') {
      filters.device = params.device.trim();
    }

    // Validate pagination params
    if (params.page) {
      filters.page = Math.max(1, Number(params.page));
    }
    if (params.limit) {
      filters.limit = Math.min(Math.max(1, Number(params.limit)), this.MAX_LIMIT);
    }

    // Validate sorting params
    if (params.sortBy && ['name', 'title'].includes(params.sortBy as string)) {
      filters.sortBy = params.sortBy as 'name' | 'title';
    }
    if (params.sortOrder && ['ASC', 'DESC'].includes((params.sortOrder as string).toUpperCase())) {
      filters.sortOrder = (params.sortOrder as string).toUpperCase() as 'ASC' | 'DESC';
    }

    return filters;
  }
}
