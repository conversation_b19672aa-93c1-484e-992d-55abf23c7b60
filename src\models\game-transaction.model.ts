import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database';
import User from './user.model';
import Game from './game.model';

export interface GameTransactionAttributes {
  id?: number;
  userId: number;
  gameId: string;
  name?: string;
  bet: number;
  win: number;
  tradeId: string;
  betInfo: string;
  matrix?: string;
  date?: Date;
  winLines?: string;
  sessionId: string;
  balance_before: number;
  balance_after: number;
  action?: string;
  createdAt?: Date;
  updatedAt?: Date;
  user?: {
    id: number;
    username: string;
  };
}

class GameTransaction extends Model<GameTransactionAttributes> implements GameTransactionAttributes {
  public id!: number;
  public userId!: number;
  public gameId!: string;
  public name!: string;
  public bet!: number;
  public win!: number;
  public tradeId!: string;
  public betInfo!: string;
  public matrix?: string;
  public date?: Date;
  public winLines?: string;
  public sessionId!: string;
  public balance_before!: number;
  public balance_after!: number;
  public action?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
  public user?: {
    id: number;
    username: string;
  };
}

GameTransaction.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
    },
    gameId: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: Game,
        key: 'id',
      },
    },
    name: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    bet: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
    },
    win: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
    },
    tradeId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    betInfo: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    matrix: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    date: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    winLines: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    sessionId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    balance_before: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
    },
    balance_after: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
    },
    action: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize,
    tableName: 'game_transactions',
    modelName: 'GameTransaction',
    timestamps: true,
  }
);

// Define associations
GameTransaction.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user'
});

export default GameTransaction;