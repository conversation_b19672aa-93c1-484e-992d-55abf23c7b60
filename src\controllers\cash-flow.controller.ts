import { Request, Response } from 'express';
import { CashFlowService } from '../services/cash-flow.service';
import { handleError } from '../utils/errors';
import User from '../models/user.model'; // Added import

export class CashFlowController {
  static async addCash(req: Request, res: Response): Promise<void> {
    try {
      const { toUserId, amount } = req.body;
      const fromUserId = req.user!.id;

      if (!toUserId || isNaN(Number(toUserId))) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid target user ID'
        });
        return;
      }

      if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
        res.status(400).json({
          status: 'error',
          message: 'Amount must be a positive number'
        });
        return;
      }

      await CashFlowService.addCash(fromUserId, Number(toUserId), Number(amount));
      res.status(200).json({ 
        status: 'success',
        message: 'Cash added successfully' 
      });
    } catch (error: any) {
      if (error.message.includes('Insufficient balance')) {
        res.status(400).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('User not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else {
        res.status(500).json({
          status: 'error',
          message: 'Internal server error'
        });
      }
    }
  }

  static async deductCash(req: Request, res: Response): Promise<void> {
    try {
      const { fromUserId, amount } = req.body;
      const requesterId = req.user!.id;

      if (!fromUserId || isNaN(Number(fromUserId))) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid target user ID'
        });
        return;
      }

      if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
        res.status(400).json({
          status: 'error',
          message: 'Amount must be a positive number'
        });
        return;
      }

      await CashFlowService.deductCash(requesterId, Number(fromUserId), Number(amount));
      res.status(200).json({ 
        status: 'success',
        message: 'Cash deducted successfully' 
      });
    } catch (error: any) {
      if (error.message.includes('Insufficient balance')) {
        res.status(400).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('User not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else {
        res.status(500).json({
          status: 'error',
          message: 'Internal server error'
        });
      }
    }
  }

  static async getCashFlowHistory(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;

      // Fetch the user object
      const user = await User.findByPk(userId);
      if (!user) {
        res.status(404).json({
          status: 'error',
          message: 'User not found'
        });
        return;
      }
      
      // Get pagination and date filter params from query
      const params = {
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
        endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined
      };

      // Pass the fetched user object for both viewer and target
      const { history, total } = await CashFlowService.getCashFlowHistory(user, user, params); 
      
      res.status(200).json({
        status: 'success',
        data: history,
        pagination: {
          page: params.page || 1,
          limit: params.limit || 20,
          total,
          totalPages: Math.ceil(total / (params.limit || 20))
        }
      });
    } catch (error: any) {
      if (error.message.includes('User not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else {
        res.status(500).json({
          status: 'error',
          message: 'Internal server error'
        });
      }
    }
  }
}
