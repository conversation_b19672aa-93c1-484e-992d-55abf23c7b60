import { Model, DataTypes } from 'sequelize';
import sequelize from '../config/database';
import User from './user.model';

export interface CashFlowAttributes {
  id: number;
  fromUserId: number;
  toUserId: number;
  amount: number;
  type: 'add' | 'deduct';
  createdAt?: Date;
  updatedAt?: Date;
}

export interface CashFlowCreationAttributes extends Omit<CashFlowAttributes, 'id'> {}

export class CashFlow extends Model<CashFlowAttributes, CashFlowCreationAttributes> implements CashFlowAttributes {
  public id!: number;
  public fromUserId!: number;
  public toUserId!: number;
  public amount!: number;
  public type!: 'add' | 'deduct';
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Associations
  public fromUser?: User;
  public toUser?: User;
}

CashFlow.init(
  {
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    fromUserId: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: User,
        key: 'id'
      }
    },
    toUserId: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: User,
        key: 'id'
      }
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      validate: {
        min: 0.01,
      }
    },
    type: {
      type: DataTypes.ENUM('add', 'deduct'),
      allowNull: false,
    }
  },
  {
    sequelize,
    tableName: 'cash_flows',
    timestamps: true,
    indexes: [
      { fields: ['fromUserId'] },
      { fields: ['toUserId'] }
    ]
  }
);

// Define associations
CashFlow.belongsTo(User, { as: 'fromUser', foreignKey: 'fromUserId' });
CashFlow.belongsTo(User, { as: 'toUser', foreignKey: 'toUserId' });

export default CashFlow;
