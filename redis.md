# Redis Integration Sprint for Game Caching

## Overview
This document outlines the implementation plan for integrating Redis caching into our casino management system to improve game data retrieval performance. Our system currently has approximately 1,238 slot games stored in the Game table, with updates occurring approximately twice a month.

## Goals
- Improve game list serving performance
- Optimize search functionality
- Reduce database load
- Handle thousands of concurrent users efficiently
- Support persistence for cache data

## Technical Requirements
- Redis server with persistence enabled
- Redis client library for Node.js
- Cache invalidation mechanism
- Error handling and fallback strategies

## Implementation Tasks

### 1. Setup Redis Infrastructure

- [ ] Install Redis server (production environment)
- [ ] Configure Redis persistence (RDB + AOF)
- [ ] Set up monitoring for Redis instance
- [ ] Add Redis connection environment variables
- [ ] Install ioredis package: `npm install ioredis`

### 2. Create Redis Service Layer

- [ ] Create `src/services/redis.service.ts` file
- [ ] Implement Redis connection with error handling
- [ ] Create methods for game data caching:
  - [ ] `getGame(gameId)` - Get single game by ID
  - [ ] `setGame(gameId, gameData)` - Cache single game
  - [ ] `getAllGames()` - Get all games
  - [ ] `setAllGames(games)` - Cache all games
  - [ ] `getSearchResults(searchParams)` - Get cached search results
  - [ ] `setSearchResults(searchParams, results)` - Cache search results
  - [ ] `getCategories()` - Get all game categories
  - [ ] `setCategories(categories)` - Cache game categories
  - [ ] `getTitles()` - Get all game titles
  - [ ] `setTitles(titles)` - Cache game titles
  - [ ] `invalidateGameCache()` - Clear all game-related caches

### 3. Update Game Service

- [ ] Modify `initGames()` method to update Redis cache after database updates
- [ ] Update `getGameUrl()` to check Redis cache before database
- [ ] Implement error handling with fallback to database

### 4. Update Game Search Service

- [ ] Modify `searchGames()` method to use Redis cache
- [ ] Update `getProvCate()` method to use Redis for categories and titles
- [ ] Implement cache key generation based on search parameters
- [ ] Add cache expiration strategy (1 hour for search results, 7 days for game data)

### 5. Environment Configuration

- [ ] Add Redis configuration to `.env.example`:
  ```
  REDIS_HOST=localhost
  REDIS_PORT=6379
  REDIS_PASSWORD=
  REDIS_DB=0
  ```
- [ ] Update production environment variables


## Code Samples

### Redis Service Implementation

```typescript
import Redis from 'ioredis';
import { logger } from '../utils/logger';

class RedisService {
  private client: Redis;
  private readonly GAME_PREFIX = 'game:';
  private readonly SEARCH_PREFIX = 'search:';
  private readonly CATEGORY_PREFIX = 'category:';
  private readonly TITLE_PREFIX = 'title:';
  private readonly ALL_GAMES_KEY = 'games:all';
  private readonly EXPIRY_TIME = 60 * 60 * 24 * 7; // 7 days in seconds

  constructor() {
    this.client = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: Number(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD,
      db: Number(process.env.REDIS_DB) || 0,
      retryStrategy: (times) => Math.min(times * 50, 2000)
    });

    this.client.on('error', (err) => {
      logger.error('Redis connection error:', err);
    });

    this.client.on('connect', () => {
      logger.info('Connected to Redis');
    });
  }

  // Game caching methods
  async getGame(gameId: string): Promise<any | null> {
    try {
      const gameData = await this.client.get(`${this.GAME_PREFIX}${gameId}`);
      return gameData ? JSON.parse(gameData) : null;
    } catch (error) {
      logger.error('Redis getGame error:', error);
      return null;
    }
  }

  async setGame(gameId: string, gameData: any): Promise<boolean> {
    try {
      await this.client.set(
        `${this.GAME_PREFIX}${gameId}`,
        JSON.stringify(gameData),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setGame error:', error);
      return false;
    }
  }

  // Cache all games
  async setAllGames(games: any[]): Promise<boolean> {
    try {
      await this.client.set(
        this.ALL_GAMES_KEY,
        JSON.stringify(games),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setAllGames error:', error);
      return false;
    }
  }

  async getAllGames(): Promise<any[] | null> {
    try {
      const gamesData = await this.client.get(this.ALL_GAMES_KEY);
      return gamesData ? JSON.parse(gamesData) : null;
    } catch (error) {
      logger.error('Redis getAllGames error:', error);
      return null;
    }
  }

  // Search results caching
  async getSearchResults(searchParams: string): Promise<any | null> {
    try {
      const key = `${this.SEARCH_PREFIX}${this.hashSearchParams(searchParams)}`;
      const data = await this.client.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis getSearchResults error:', error);
      return null;
    }
  }

  async setSearchResults(searchParams: string, results: any): Promise<boolean> {
    try {
      const key = `${this.SEARCH_PREFIX}${this.hashSearchParams(searchParams)}`;
      await this.client.set(key, JSON.stringify(results), 'EX', 3600); // 1 hour expiry for search results
      return true;
    } catch (error) {
      logger.error('Redis setSearchResults error:', error);
      return false;
    }
  }

  // Categories and titles caching
  async setCategories(categories: string[]): Promise<boolean> {
    try {
      await this.client.set(
        this.CATEGORY_PREFIX + 'all',
        JSON.stringify(categories),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setCategories error:', error);
      return false;
    }
  }

  async getCategories(): Promise<string[] | null> {
    try {
      const data = await this.client.get(this.CATEGORY_PREFIX + 'all');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis getCategories error:', error);
      return null;
    }
  }

  async setTitles(titles: string[]): Promise<boolean> {
    try {
      await this.client.set(
        this.TITLE_PREFIX + 'all',
        JSON.stringify(titles),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setTitles error:', error);
      return false;
    }
  }

  async getTitles(): Promise<string[] | null> {
    try {
      const data = await this.client.get(this.TITLE_PREFIX + 'all');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis getTitles error:', error);
      return null;
    }
  }

  // Cache invalidation
  async invalidateGameCache(): Promise<void> {
    try {
      // Get all keys with game prefix
      const keys = await this.client.keys(`${this.GAME_PREFIX}*`);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }
      
      // Delete all games key
      await this.client.del(this.ALL_GAMES_KEY);
      
      // Delete categories and titles
      await this.client.del(this.CATEGORY_PREFIX + 'all');
      await this.client.del(this.TITLE_PREFIX + 'all');
      
      // Delete search results
      const searchKeys = await this.client.keys(`${this.SEARCH_PREFIX}*`);
      if (searchKeys.length > 0) {
        await this.client.del(...searchKeys);
      }
      
      logger.info('Game cache invalidated');
    } catch (error) {
      logger.error('Redis invalidateGameCache error:', error);
    }
  }

  // Helper method to create a consistent hash for search parameters
  private hashSearchParams(searchParams: string): string {
    return Buffer.from(searchParams).toString('base64');
  }
}

export const redisService = new RedisService();
```

### Game Service Update Example

```typescript
// Add this import at the top
import { redisService } from './redis.service';

// Modify the initGames method to update Redis cache
static async initGames(): Promise<boolean> {
  try {
    const response = await axios.post<GameListResponse>(this.endpoint, {
      cmd: 'gamesList',
      hall: this.hallID,
      key: this.hallKey,
      cdnUrl: '',
    });

    if (response.data.status === 'success') {
      // Clear existing games
      await Game.destroy({ where: {} });

      // Create new games
      const games = await Promise.all(
        response.data.content.gameList.map((game) =>
          Game.create({
            id: game.id,
            name: game.name,
            title: game.title,
            img: game.img,
            device: game.device,
            categories: game.categories,
            flash: game.flash,
          })
        )
      );
      
      // Clear the cache after successful initialization
      gameCache.del(CACHE_KEYS.CATEGORIES);
      gameCache.del(CACHE_KEYS.TITLES);
      
      // Update Redis cache
      await redisService.invalidateGameCache();
      await redisService.setAllGames(games);
      
      // Extract and cache categories and titles
      const categories = await Game.getDistinctCategories();
      const titles = await Game.getDistinctTitles();
      await redisService.setCategories(categories);
      await redisService.setTitles(titles);
      
      console.log('Game cache updated after initialization');
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error initializing games:', error);
    return false;
  }
}
```

### Game Search Service Update Example

```typescript
// Add this import at the top
import { redisService } from './redis.service';

// Modify searchGames method to use Redis cache
static async searchGames(filters: GameSearchFilters): Promise<PaginatedResponse> {
  try {
    const {
      name,
      title,
      categories,
      device,
      page = 1,
      limit = this.DEFAULT_LIMIT,
      sortBy = 'name',
      sortOrder = 'ASC'
    } = filters;

    // Create a cache key based on search parameters
    const cacheKey = JSON.stringify({
      name, title, categories, device, page, limit, sortBy, sortOrder
    });

    // Try to get from cache first
    const cachedResults = await redisService.getSearchResults(cacheKey);
    if (cachedResults) {
      return cachedResults;
    }

    // If not in cache, perform the search
    // ... existing search logic ...

    // Cache the results
    await redisService.setSearchResults(cacheKey, result);

    return result;
  } catch (error) {
    console.error('Game search error:', error);
    throw error;
  }
}
```

