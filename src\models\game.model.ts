import { DataTypes, Model, Op } from 'sequelize';
import sequelize from '../config/database';

export interface GameAttributes {
  id: string;
  name: string;
  title: string;
  img: string;
  device: string;
  categories: string;
  flash: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

class Game extends Model<GameAttributes> implements GameAttributes {
  public id!: string;
  public name!: string;
  public title!: string;
  public img!: string;
  public device!: string;
  public categories!: string;
  public flash!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Helper methods for categories
  public getCategoriesArray(): string[] {
    return this.categories ? this.categories.split(',').map(c => c.trim()) : [];
  }

  public static getCategoriesWhereClause(categories: string[]): any {
    return {
      [Op.or]: categories.map(category => ({
        categories: {
          [Op.like]: `%${category}%`
        }
      }))
    };
  }

  public static async getDistinctCategories(): Promise<string[]> {
    const games = await this.findAll({
      attributes: ['categories'],
      group: ['categories']
    });

    const categories = games
      .flatMap(game => game.getCategoriesArray())
      .map(cat => cat.toLowerCase())
      .filter((value, index, self) => self.indexOf(value) === index);

    return categories;
  }

  public static async getDistinctTitles(): Promise<string[]> {
    const games = await this.findAll({
      attributes: ['title'],
      group: ['title']
    });

    return games
      .map(game => game.title.toLowerCase())
      .filter((value, index, self) => self.indexOf(value) === index);
  }
}

Game.init(
  {
    id: {
      type: DataTypes.STRING,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    img: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    device: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    categories: {
      type: DataTypes.STRING,
      allowNull: false,
      get() {
        const rawValue = this.getDataValue('categories');
        return rawValue || '';
      },
      set(value: string | string[]) {
        if (Array.isArray(value)) {
          this.setDataValue('categories', value.join(','));
        } else {
          this.setDataValue('categories', value);
        }
      }
    },
    flash: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
  },
  {
    sequelize,
    modelName: 'Game',
    tableName: 'games',
    timestamps: true,
  }
);

export default Game;
