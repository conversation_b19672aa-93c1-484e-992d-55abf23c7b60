import { Request, Response } from 'express';
import { CashFlowService } from '../services/cash-flow.service';
import User from '../models/user.model'; // Added import

interface PaginationParams {
  page?: number;
  limit?: number;
  startDate?: Date;
  endDate?: Date;
  type?: 'add' | 'deduct';
}

export class CashFlowHistoryController {
  private static readonly DEFAULT_PAGE = 1;
  private static readonly DEFAULT_LIMIT = 20;
  private static readonly MAX_LIMIT = 100;

  private static getPaginationParams(query: any): PaginationParams {
    const params: PaginationParams = {
      page: Math.max(1, Number(query.page) || this.DEFAULT_PAGE),
      limit: Math.min(
        this.MAX_LIMIT,
        Math.max(1, Number(query.limit) || this.DEFAULT_LIMIT)
      )
    };

    // Parse dates if provided
    if (query.startDate) {
      const startDate = new Date(query.startDate);
      if (!isNaN(startDate.getTime())) {
        params.startDate = startDate;
      }
    }

    if (query.endDate) {
      const endDate = new Date(query.endDate);
      if (!isNaN(endDate.getTime())) {
        params.endDate = endDate;
      }
    }

    // Add transaction type filter if provided
    if (query.type && (query.type === 'add' || query.type === 'deduct')) {
      params.type = query.type;
    }

    return params;
  }

  // Get cash flow history for a specific user
  static async getCashFlowHistory(req: Request, res: Response): Promise<void> {
    try {
      const viewerId = req.user!.id;
      const targetUserId = parseInt(req.params.userId);
      console.log('Getting history for viewer:', viewerId, 'target:', targetUserId);

      if (isNaN(targetUserId)) {
        res.status(400).json({
          status: 'error',
          message: 'Invalid user ID'
        });
        return;
      }

      // Fetch viewer and target user ONCE here
      const [viewer, targetUser] = await Promise.all([
          User.findByPk(viewerId),
          User.findByPk(targetUserId)
      ]);

      if (!viewer || !targetUser) {
          res.status(404).json({ // Removed 'return'
              status: 'error',
              message: 'User not found'
          });
          return; // Explicitly return to satisfy TypeScript control flow
      }

      const params = CashFlowHistoryController.getPaginationParams(req.query);
      console.log('Pagination params:', params);

      // Pass fetched user objects to the service
      const { history, total } = await CashFlowService.getCashFlowHistory(
        viewer, // Pass object
        targetUser, // Pass object
        params
      );
      console.log('Found total records:', total);

      res.status(200).json({
        status: 'success',
        data: history,
        pagination: {
          page: params.page,
          limit: params.limit,
          total,
          totalPages: Math.ceil(total / (params.limit || CashFlowHistoryController.DEFAULT_LIMIT))
        }
      });
    } catch (error: any) {
      console.error('Error in getCashFlowHistory:', error);

      if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else {
        res.status(500).json({
          status: 'error',
          message: error.message || 'Internal server error'
        });
      }
    }
  }

  // Get all history based on role
  static async getAllHistory(req: Request, res: Response): Promise<void> {
    try {
      const viewerId = req.user!.id;
      console.log('Getting all history for viewer:', viewerId);

      const params = CashFlowHistoryController.getPaginationParams(req.query);
      console.log('Pagination params:', params);

      const { history, total } = await CashFlowService.getAllHistory(viewerId, params);
      console.log('Found total records:', total);

      res.status(200).json({
        status: 'success',
        data: history,
        pagination: {
          page: params.page,
          limit: params.limit,
          total,
          totalPages: Math.ceil(total / (params.limit || CashFlowHistoryController.DEFAULT_LIMIT))
        }
      });
    } catch (error: any) {
      console.error('Error in getAllHistory:', error);

      if (error.message.includes('Unauthorized')) {
        res.status(403).json({
          status: 'error',
          message: error.message
        });
      } else if (error.message.includes('not found')) {
        res.status(404).json({
          status: 'error',
          message: error.message
        });
      } else {
        res.status(500).json({
          status: 'error',
          message: error.message || 'Internal server error'
        });
      }
    }
  }
}
