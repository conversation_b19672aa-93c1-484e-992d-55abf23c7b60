import express, { Express, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import userRoutes from './routes/user.routes';
import cashFlowRoutes from './routes/cash-flow.routes';
import gameRoutes from './routes/game.routes';
import { apiLimiter, authLimiter, sensitiveOpsLimiter, gameLimiter } from './middleware/rate-limit.middleware';
import { initDatabase } from './config/database';
import { redisService } from './services/redis.service';

dotenv.config();

const app: Express = express();
const port = process.env.PORT || 3000;

// Enhanced Security Middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https://static.cdneu-stat.com"],
      fontSrc: ["'self'"],
      connectSrc: ["'self'"],
      mediaSrc: ["'self'"],
      objectSrc: ["'none'"],
      frameSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"]
    }
  },
  crossOriginEmbedderPolicy: true,
  crossOriginOpenerPolicy: { policy: "same-origin" },
  crossOriginResourcePolicy: { policy: "same-origin" },
  dnsPrefetchControl: { allow: true },
  frameguard: { action: "deny" },
  hidePoweredBy: true,
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: { permittedPolicies: "none" },
  referrerPolicy: { policy: "strict-origin-when-cross-origin" },
  xssFilter: true
}));

// Parse allowed origins from environment variables
const allowedOrigins = process.env.ALLOWED_ORIGINS
  ? process.env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim())
  : [];

// Strictly define allowed origins
const strictAllowedOrigins = [
  'http://localhost:3001',  // Admin frontend
  'https://localhost:3001', // Admin frontend (HTTPS)
  'http://localhost:3002',  // Player frontend
  'https://localhost:3002'  // Player frontend (HTTPS)
];

// Production origins - use environment variable or fallback
const prodOrigins = allowedOrigins.length > 0
  ? allowedOrigins
  : [process.env.FRONTEND_URL || 'https://your-production-domain.com'];

// Enhanced CORS Configuration with strict origin checking
app.use(cors({
  origin: function(origin, callback) {
    // For security in production, don't allow requests with no origin
    if (!origin) {
      if (process.env.NODE_ENV === 'production') {
        console.warn('Request with no origin rejected in production');
        return callback(new Error('Origin required'), false);
      } else {
        // Only allow in development for testing
        return callback(null, true);
      }
    }

    // Use strict origins list in both dev and production
    const allowList = process.env.NODE_ENV === 'production' ? prodOrigins : strictAllowedOrigins;

    if (allowList.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`Origin ${origin} not allowed by CORS policy`);
      callback(new Error('Not allowed by CORS policy'), false);
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
  credentials: true,
  maxAge: 86400,
  preflightContinue: false,
  optionsSuccessStatus: 204
}));

// Additional Security Headers
app.use((_req: Request, res: Response, next: NextFunction) => {
  res.setHeader('Permissions-Policy', '');
  res.setHeader('X-Permitted-Cross-Domain-Policies', 'none');
  //res.setHeader('Clear-Site-Data', '"cache", "cookies", "storage"');
  next();
});

// Body Parsing
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));

// Cookie Parsing
app.use(cookieParser());

// Apply rate limiting
app.use('/api/', apiLimiter);
app.use('/api/users/login', authLimiter);
app.use(['/api/users/create', '/api/users/:userId/password'], sensitiveOpsLimiter);
app.use('/api/v1/games', gameLimiter);

// Routes
app.use('/api/users', userRoutes);
app.use('/api/v1/cashflow', cashFlowRoutes);
app.use('/api/v1/games', gameRoutes);

// Health Check
app.get('/health', async (_req: Request, res: Response) => {
  const redisConnected = await redisService.isConnected();

  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    services: {
      redis: redisConnected ? 'connected' : 'disconnected'
    }
  });
});

// 404 Handler
app.use((_req: Request, res: Response) => {
  res.status(404).json({
    status: 'error',
    message: 'Route not found',
    documentation: 'https://your-api-docs.com'
  });
});

// Global Error Handler
app.use((err: Error, _req: Request, res: Response, _next: NextFunction) => {
  console.error(`[${new Date().toISOString()}] Error: ${err.message}`);
  console.error(err.stack);

  res.status(500).json({
    status: 'error',
    message: 'Internal server error',
    //requestId: req.id,
    error: process.env.NODE_ENV === 'development' ? {
      message: err.message,
      stack: err.stack
    } : undefined
  });
});

// Server Startup
const startServer = async (): Promise<void> => {
  try {
    // Initialize database
    await initDatabase();
    console.log(`[${new Date().toISOString()}] Database initialized successfully`);

    app.listen(port, () => {
      console.log(`[${new Date().toISOString()}] Server running on port ${port}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Server startup failed:`, error);
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async (signal: string): Promise<void> => {
  console.log(`[${new Date().toISOString()}] Received ${signal}. Starting graceful shutdown...`);

  try {
    // Disconnect Redis
    await redisService.disconnect();
    console.log(`[${new Date().toISOString()}] Graceful shutdown completed`);
    process.exit(0);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error during graceful shutdown:`, error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

startServer();