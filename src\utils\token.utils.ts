import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import User, { UserAttributes, UserRole } from '../models/user.model';
import Token from '../models/token.model';
import { Op } from 'sequelize';

export interface JwtPayload {
  id: number;
  username: string;
  role: UserRole;
  tokenId: string;
  _verified?: boolean;
}

export class TokenUtils {
  private static readonly JWT_ACCESS_SECRET: jwt.Secret = process.env.JWT_ACCESS_SECRET || 'your-access-secret-key';
  private static readonly JWT_REFRESH_SECRET: jwt.Secret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';
  private static readonly ACCESS_TOKEN_EXPIRES_IN: string = process.env.ACCESS_TOKEN_EXPIRES_IN || '15m';
  private static readonly REFRESH_TOKEN_EXPIRES_IN: string = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';

  /**
   * Generate token pair (access + refresh) and store in database
   */
  static async generateTokenPair(
    user: UserAttributes,
    deviceInfo: string,
    ipAddress: string,
    options?: { invalidateExisting?: boolean } // Add options parameter
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const tokenId = uuidv4();

    // Conditionally invalidate existing tokens (default is true)
    if (options?.invalidateExisting !== false) {
      await this.invalidateAllUserTokens(user.id);
    }

    const accessToken = jwt.sign(
      {
        id: user.id,
        username: user.username,
        role: user.role,
        tokenId,
      } as JwtPayload,
      this.JWT_ACCESS_SECRET,
      { expiresIn: this.ACCESS_TOKEN_EXPIRES_IN } as jwt.SignOptions
    );

    const refreshToken = jwt.sign(
      {
        userId: user.id,
        tokenId,
      },
      this.JWT_REFRESH_SECRET,
      { expiresIn: this.REFRESH_TOKEN_EXPIRES_IN } as jwt.SignOptions
    );

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Create new token record
    await Token.create({
      id: tokenId,
      userId: user.id,
      refreshToken,
      accessToken,
      deviceInfo,
      ipAddress,
      isValid: true,
      expiresAt,
      lastUsed: new Date(),
    });

    return {
      accessToken,
      refreshToken,
    };
  }

  /**
   * Get verified token record with user data
   */
  private static async getVerifiedTokenRecord(tokenId: string) {
    const tokenRecord = await Token.findOne({
      where: { id: tokenId },
      include: [{
        model: User,
        as: 'User',
        attributes: ['id', 'username', 'role'],
      }],
    });

    if (!tokenRecord || !tokenRecord.User) {
      throw new Error('Token not found');
    }

    return tokenRecord;
  }

  /**
   * Verify access token and return decoded payload with user data
   */
  private static readonly tokenCache = new Map<string, JwtPayload>();

  static async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      // Skip if already a verified payload object
      if (typeof token !== 'string') {
        return token as JwtPayload;
      }

      // Check cache first
      if (this.tokenCache.has(token)) {
        return this.tokenCache.get(token)!;
      }

      // Verify JWT signature and decode
      const decoded = jwt.verify(token, this.JWT_ACCESS_SECRET, {} as jwt.VerifyOptions) as JwtPayload;

      // Validate role
      if (!Object.values(UserRole).includes(decoded.role)) {
        throw new Error('Invalid user role in token');
      }

      // Verify token in database
      const tokenRecord = await Token.findOne({
        where: {
          id: decoded.tokenId,
          isValid: true,
          expiresAt: { [Op.gt]: new Date() }
        },
        attributes: ['id'], // Only fetch needed fields
        raw: true
      });

      if (!tokenRecord) {
        throw new Error('Invalid token');
      }

      // Cache the verified payload
      this.tokenCache.set(token, decoded);
      setTimeout(() => this.tokenCache.delete(token), 60000); // Cache for 60 seconds

      return decoded;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired');
      }
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      }
      throw error;
    }
  }

  /**
   * Verify refresh token and generate new token pair
   */
  static async verifyRefreshToken(
    refreshToken: string,
    deviceInfo: string,
    ipAddress: string
  ): Promise<{ accessToken: string; refreshToken: string; userId: number }> {
    try {
      const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET, {} as jwt.VerifyOptions) as {
        userId: number;
        tokenId: string;
      };

      // Get and verify token record
      const tokenRecord = await Token.findOne({
        where: {
          id: decoded.tokenId,
          refreshToken,
          expiresAt: { [Op.gt]: new Date() },
        },
      });

      if (!tokenRecord) {
        throw new Error('Refresh token not found');
      }

      // Delete the old refresh token
      await Token.destroy({
        where: { id: decoded.tokenId }
      });

      // Get user from verified token record
      const verifiedRecord = await this.getVerifiedTokenRecord(decoded.tokenId);
      const user = verifiedRecord.User;

      if (!user) {
        throw new Error('User not found');
      }

      const tokens = await this.generateTokenPair(user, deviceInfo, ipAddress);

      return { ...tokens, userId: decoded.userId };
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token expired');
      }
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * Verify refresh token and issue a new token pair
   */
  static async refreshTokenPair(
    refreshToken: string
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // First verify the JWT structure
      const decoded = jwt.verify(refreshToken, this.JWT_REFRESH_SECRET, {} as jwt.VerifyOptions) as {
        userId: number;
        tokenId: string;
      };

      // Clear old token from cache
      for (const [token, payload] of this.tokenCache) {
        if (payload.tokenId === decoded.tokenId) {
          this.tokenCache.delete(token);
        }
      }

      // Then find the token record with user data
      const tokenRecord = await Token.findOne({
        where: {
          id: decoded.tokenId,
          refreshToken,
          isValid: true,
          expiresAt: { [Op.gt]: new Date() },
        },
        include: [{
          model: User,
          as: 'User',
          attributes: ['id', 'username', 'role', 'is_active'],
          required: true
        }]
      });

      if (!tokenRecord) {
        throw new Error('Invalid refresh token');
      }

      // Check if user is active
      if (!tokenRecord.User?.is_active) {
        throw new Error('User account is inactive');
      }

      // Invalidate old token
      await this.invalidateToken(tokenRecord.id);

      // Generate new tokens

      return this.generateTokenPair(tokenRecord.User, 'web', '0.0.0.0', { invalidateExisting: false }); // Pass the option

    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Refresh token has expired');
      }
      // Re-throw other errors or handle them as needed
      throw error; // Ensure other errors like the security concern are propagated
    }
  }

  /**
   * Invalidate and delete a specific token
   */
  static async invalidateToken(tokenId: string): Promise<void> {
    // Clear from cache first
    for (const [token, payload] of this.tokenCache.entries()) {
      if (payload.tokenId === tokenId) {
        this.tokenCache.delete(token);
      }
    }

    // Then delete from database
    await Token.destroy({
      where: { id: tokenId }
    });
  }

  /**
   * Invalidate and delete all tokens for a user
   */
  static async invalidateAllUserTokens(userId: number): Promise<void> {
    // Clear all cached tokens for this user
    for (const [token, payload] of this.tokenCache) {
      if (payload.id === userId) {
        this.tokenCache.delete(token);
        console.log(`Cleared cached token ${token.slice(0, 10)}... for user ${userId}`);
      }
    }

    // Delete from database
    await Token.destroy({
      where: { userId }
    });
    console.log(`Invalidated all database tokens for user ${userId}`);
  }

  /**
   * Clean up expired and invalid tokens
   */
  static async cleanupExpiredTokens(): Promise<void> {
    await Token.destroy({
      where: {
        [Op.or]: [
          {
            expiresAt: {
              [Op.lt]: new Date(),
            },
          },
          {
            isValid: false,
          },
        ],
      },
    });
  }

  /**
   * Generate a CSRF token for protection against CSRF attacks
   */
  static generateCsrfToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Verify a CSRF token
   */
  static verifyCsrfToken(token: string, providedToken: string): boolean {
    // Use constant-time comparison to prevent timing attacks
    return crypto.timingSafeEqual(
      Buffer.from(token),
      Buffer.from(providedToken)
    );
  }
}
