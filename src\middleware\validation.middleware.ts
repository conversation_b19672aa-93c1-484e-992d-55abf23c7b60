import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator/src/validation-result';
import { Validation<PERSON>hain } from 'express-validator/src/chain';
import { ValidationError } from 'express-validator/src/base';

export const validateRequest = (
    req: Request,
    res: Response,
    next: NextFunction
) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            status: 'error',
            errors: errors.array().map((err: ValidationError) => ({
                field: err.param,
                message: err.msg
            }))
        });
    }
    next();
};

export const validate = (validations: ValidationChain[]) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        await Promise.all(validations.map(validation => validation.run(req)));
        validateRequest(req, res, next);
    };
};
