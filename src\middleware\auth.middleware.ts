import { Request, Response, NextFunction } from 'express';
import { JwtPayload } from '../utils/token.utils';
import { TokenUtils } from '../utils/token.utils';
import { UserRole } from '../models/user.model';

declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

export const authenticate = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<Response | void> => {
  try {
    // Get token from cookie only
    const token = req.cookies?.accessToken;

    // If no token found
    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: 'Authentication required'
      });
    }

    try {
      // Verify and decode token - this also checks validity and expiration
      const decoded = await TokenUtils.verifyAccessToken(token);
      decoded._verified = true; // Mark as verified
      req.user = decoded;
      next();
    } catch (error) {
      if (error instanceof Error) {
        return res.status(401).json({
          status: 'error',
          message: error.message
        });
      }
      throw error;
    }
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(401).json({
      status: 'error',
      message: 'Authentication failed'
    });
  }
};

export const authorize = (roles: UserRole[]) => {
  return (req: Request, res: Response, next: NextFunction): void | Response => {
    if (!req.user) {
      return res.status(401).json({ message: 'User not authenticated' });
    }

    if (!roles.includes(req.user.role as UserRole)) {
      return res.status(403).json({ message: 'Insufficient permissions' });
    }

    next();
  };
};

export const isOwnerOrSelf = (
  req: Request,
  res: Response,
  next: NextFunction
): void | Response => {
  if (!req.user) {
    return res.status(401).json({ message: 'User not authenticated' });
  }

  const userId = parseInt(req.params.userId);
  if (isNaN(userId)) {
    return res.status(400).json({ message: 'Invalid user ID' });
  }

  if (req.user.role === UserRole.OWNER || req.user.id === userId) {
    next();
  } else {
    return res.status(403).json({ message: 'Insufficient permissions' });
  }
};
