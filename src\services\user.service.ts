import { Op, QueryTypes } from 'sequelize';
import User, { UserRole } from '../models/user.model';
import sequelize from '../config/database'; // Ensure you have Sequelize instance imported
import { ForbiddenError, NotFoundError } from '../utils/errors';

interface SearchFilters {
  username?: string;
  role?: UserRole[];
  isBanned?: boolean;
  isActive?: boolean;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
  level?: number;
}

export class UserService {
  static async getDescendantIds(userId: number): Promise<number[]> {
    const query = `
      WITH RECURSIVE UserHierarchy AS (
        -- Anchor member: Select direct children
        SELECT id
        FROM users
        WHERE parent_id = :userId
        UNION ALL
        -- Recursive member: Select children of the previous level
        SELECT u.id
        FROM users u
        INNER JOIN UserHierarchy uh ON u.parent_id = uh.id
      )
      SELECT id FROM UserHierarchy;
    `;
    try {
      const results = await sequelize.query(query, {
        replacements: { userId },
        type: QueryTypes.SELECT,
      });
      // Ensure results is an array of objects with 'id' property
      if (Array.isArray(results)) {
        return results.map((row: any) => row.id);
      }
      return []; // Return empty array if results format is unexpected
    } catch (error) {
      console.error('Error fetching descendant IDs:', error);
      throw new Error('Failed to retrieve user hierarchy');
    }
  }
  static async searchUsers(searcherId: number, filters: SearchFilters) {
    const searcher = await User.findByPk(searcherId);
    if (!searcher) {
      throw new NotFoundError('User not found');
    }

    // Ensure the user has permission to search
    if (![UserRole.SUPEROWNER, UserRole.OWNER, UserRole.SUPERADMIN, UserRole.ADMIN, UserRole.CASHIER].includes(searcher.role)) {
      throw new ForbiddenError('Unauthorized to perform user search');
    }

    // Default to level 2 (full hierarchy) if not specified
    const level = filters.level === 1 ? 1 : null;

    // SQL Query for fetching hierarchical descendants
    const descendantQuery = level === 1 ? 
      // Level 1 - direct children only
      `SELECT id FROM users WHERE parent_id = :searcherId`
      :
      // Level 2+ - full hierarchy
      `WITH RECURSIVE UserHierarchy AS (
        SELECT id, parent_id FROM users WHERE parent_id = :searcherId
        UNION ALL
        SELECT u.id, u.parent_id
        FROM users u
        INNER JOIN UserHierarchy uh ON u.parent_id = uh.id
      )
      SELECT id FROM UserHierarchy`;

    // Execute raw query to get descendant IDs
    const descendantIds = await sequelize.query(descendantQuery, {
      replacements: { searcherId },
      type: QueryTypes.SELECT
    });

    const userIds = descendantIds.map((row: any) => row.id); // Extract ID array

    // Build where clause
    const where: any = { id: { [Op.in]: userIds } };

    // Username filter
    if (filters.username) {
      where.username = { [Op.like]: `%${filters.username}%` };
    }

    // Role filter
    if (filters.role) {
      where.role = { [Op.in]: filters.role };
    }

    // Ban status filter
    if (typeof filters.isBanned !== 'undefined') {
      where.is_banned = filters.isBanned;
    }

    // Active status filter
    if (typeof filters.isActive !== 'undefined') {
      where.is_active = filters.isActive;
    }

    // Date range filter
    if (filters.startDate || filters.endDate) {
      where.created_at = {};
      if (filters.startDate) where.created_at[Op.gte] = filters.startDate;
      if (filters.endDate) where.created_at[Op.lte] = filters.endDate;
    }

    // Pagination
    const page = filters.page || 1;
    const limit = filters.limit || 10;
    const offset = (page - 1) * limit;

    // Fetch users with pagination
    const { count, rows } = await User.findAndCountAll({
      where,
      attributes: [
        'id', 'username', 'role', 'balance', 'currency',
        'is_active', 'is_banned', 'last_login',
        'created_at', 'updated_at', 'parent_id'
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });

    return {
      users: rows,
      pagination: {
        total: count,
        page,
        totalPages: Math.ceil(count / limit),
        limit
      }
    };
  }
}
